"""
文件处理模块
提供线程安全的文件读写操作
"""

import os
import threading
import time
import logging
from typing import Optional, List, Dict, Any
from src.config import config

logger = logging.getLogger(__name__)


class ThreadSafeFileHandler:
    """线程安全的文件处理器"""
    
    def __init__(self):
        """初始化文件处理器"""
        self.locks = {
            'input': threading.Lock(),
            'success': threading.Lock(),
            'failed': threading.Lock(),
            'cache': threading.Lock()
        }
        
        # 获取文件路径
        self.file_paths = config.get_file_paths()
        
        # 确保文件存在
        self._ensure_files_exist()
    
    def _ensure_files_exist(self):
        """确保所有必要文件存在"""
        try:
            # 创建输入文件（如果不存在）
            if not os.path.exists(self.file_paths['input_file']):
                with open(self.file_paths['input_file'], 'w', encoding='utf-8') as f:
                    f.write("")
                logger.info(f"创建输入文件: {self.file_paths['input_file']}")
            
            # 创建其他文件（如果不存在）
            for file_type in ['success_file', 'failed_file', 'cache_file']:
                file_path = self.file_paths[file_type]
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("")
                    logger.info(f"创建{file_type}文件: {file_path}")
                    
        except Exception as e:
            logger.error(f"创建文件失败: {e}")
    
    def read_next_line(self) -> Optional[str]:
        """
        从输入文件读取下一行并删除该行（线程安全）
        
        Returns:
            Optional[str]: 读取的行内容，如果文件为空返回None
        """
        with self.locks['input']:
            try:
                input_file = self.file_paths['input_file']
                
                # 读取所有行
                with open(input_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 如果文件为空
                if not lines:
                    return None
                
                # 获取第一行
                first_line = lines[0].strip()
                
                # 如果第一行为空，继续查找非空行
                while lines and not first_line:
                    lines.pop(0)
                    if lines:
                        first_line = lines[0].strip()
                
                if not first_line:
                    return None
                
                # 删除第一行并重写文件
                remaining_lines = lines[1:]
                with open(input_file, 'w', encoding='utf-8') as f:
                    f.writelines(remaining_lines)
                
                logger.info(f"读取并删除行: {first_line[:50]}...")
                return first_line
                
            except Exception as e:
                logger.error(f"读取输入文件失败: {e}")
                return None
    
    def write_success_record(self, data: str):
        """
        写入成功记录（线程安全）
        
        Args:
            data: 要写入的数据
        """
        with self.locks['success']:
            try:
                success_file = self.file_paths['success_file']
                with open(success_file, 'a', encoding='utf-8') as f:
                    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp}] {data}\n")
                logger.info(f"写入成功记录: {data[:50]}...")
            except Exception as e:
                logger.error(f"写入成功记录失败: {e}")
    
    def write_failed_record(self, data: str, error: str = ""):
        """
        写入失败记录（线程安全）
        
        Args:
            data: 要写入的数据
            error: 错误信息
        """
        with self.locks['failed']:
            try:
                failed_file = self.file_paths['failed_file']
                with open(failed_file, 'a', encoding='utf-8') as f:
                    # 只写入原始数据，不添加时间戳，方便重新运行
                    f.write(f"{data}\n")
                logger.info(f"写入失败记录: {data[:50]}...")
            except Exception as e:
                logger.error(f"写入失败记录失败: {e}")
    
    def write_cache_record(self, task_id: str, data: Dict[str, Any]):
        """
        写入缓存记录（线程安全）

        Args:
            task_id: 任务ID
            data: 缓存数据
        """
        with self.locks['cache']:
            try:
                cache_file = self.file_paths['cache_file']
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

                cache_entry = {
                    "timestamp": timestamp,
                    "task_id": task_id,
                    "data": data
                }

                with open(cache_file, 'a', encoding='utf-8') as f:
                    import json
                    f.write(json.dumps(cache_entry, ensure_ascii=False) + "\n")

                logger.info(f"写入缓存记录: {task_id}")
            except Exception as e:
                logger.error(f"写入缓存记录失败: {e}")

    def save_network_data(self, session_id: str, requests_data: List[Dict[str, Any]], analysis_result: Dict[str, Any] = None):
        """
        保存网络抓包数据（线程安全）

        Args:
            session_id: 会话ID
            requests_data: 请求数据列表
            analysis_result: 分析结果
        """
        try:
            # 创建网络数据目录
            network_dir = "network_data"
            os.makedirs(network_dir, exist_ok=True)

            timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")

            # 保存原始请求数据
            raw_file = os.path.join(network_dir, f"requests_{session_id}_{timestamp}.json")
            with open(raw_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "session_id": session_id,
                    "timestamp": timestamp,
                    "request_count": len(requests_data),
                    "requests": requests_data
                }, f, indent=2, ensure_ascii=False)

            # 保存分析结果
            if analysis_result:
                analysis_file = os.path.join(network_dir, f"analysis_{session_id}_{timestamp}.json")
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    json.dump(analysis_result, f, indent=2, ensure_ascii=False)

            # 更新索引文件
            index_file = os.path.join(network_dir, "session_index.txt")
            with open(index_file, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp} | {session_id} | {len(requests_data)} requests\n")

            logger.info(f"网络数据已保存: {session_id}, {len(requests_data)} 个请求")

        except Exception as e:
            logger.error(f"保存网络数据失败: {e}")
    
    def get_input_file_line_count(self) -> int:
        """
        获取输入文件行数
        
        Returns:
            int: 行数
        """
        try:
            input_file = self.file_paths['input_file']
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 计算非空行数
            non_empty_lines = [line for line in lines if line.strip()]
            return len(non_empty_lines)
            
        except Exception as e:
            logger.error(f"获取输入文件行数失败: {e}")
            return 0
    
    def get_success_count(self) -> int:
        """
        获取成功记录数量
        
        Returns:
            int: 成功记录数量
        """
        try:
            success_file = self.file_paths['success_file']
            if not os.path.exists(success_file):
                return 0
            
            with open(success_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            return len([line for line in lines if line.strip()])
            
        except Exception as e:
            logger.error(f"获取成功记录数量失败: {e}")
            return 0
    
    def get_failed_count(self) -> int:
        """
        获取失败记录数量
        
        Returns:
            int: 失败记录数量
        """
        try:
            failed_file = self.file_paths['failed_file']
            if not os.path.exists(failed_file):
                return 0
            
            with open(failed_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            return len([line for line in lines if line.strip()])
            
        except Exception as e:
            logger.error(f"获取失败记录数量失败: {e}")
            return 0
    
    def clear_cache_file(self):
        """清空缓存文件"""
        with self.locks['cache']:
            try:
                cache_file = self.file_paths['cache_file']
                with open(cache_file, 'w', encoding='utf-8') as f:
                    f.write("")
                logger.info("缓存文件已清空")
            except Exception as e:
                logger.error(f"清空缓存文件失败: {e}")
    
    def backup_files(self, backup_suffix: str = None):
        """
        备份所有文件
        
        Args:
            backup_suffix: 备份文件后缀，默认使用时间戳
        """
        try:
            if backup_suffix is None:
                backup_suffix = time.strftime("%Y%m%d_%H%M%S")
            
            for file_type, file_path in self.file_paths.items():
                if os.path.exists(file_path):
                    backup_path = f"{file_path}.backup_{backup_suffix}"
                    
                    with open(file_path, 'r', encoding='utf-8') as src:
                        content = src.read()
                    
                    with open(backup_path, 'w', encoding='utf-8') as dst:
                        dst.write(content)
                    
                    logger.info(f"备份文件: {file_path} -> {backup_path}")
            
        except Exception as e:
            logger.error(f"备份文件失败: {e}")
    
    def get_file_status(self) -> Dict[str, Any]:
        """
        获取文件状态信息
        
        Returns:
            Dict[str, Any]: 文件状态信息
        """
        status = {}
        
        try:
            for file_type, file_path in self.file_paths.items():
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    status[file_type] = {
                        "path": file_path,
                        "exists": True,
                        "line_count": len([line for line in lines if line.strip()]),
                        "size_bytes": os.path.getsize(file_path)
                    }
                else:
                    status[file_type] = {
                        "path": file_path,
                        "exists": False,
                        "line_count": 0,
                        "size_bytes": 0
                    }
            
            return status
            
        except Exception as e:
            logger.error(f"获取文件状态失败: {e}")
            return {}


# 全局文件处理器实例
file_handler = ThreadSafeFileHandler()
