#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目打包脚本
将整个项目打包成zip文件，排除不必要的文件
"""

import os
import zipfile
import datetime
from pathlib import Path

def create_project_package():
    """创建项目打包文件"""
    
    # 获取当前时间作为版本号
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"qishui004_v{timestamp}.zip"
    
    # 需要排除的文件和目录
    exclude_patterns = {
        '__pycache__',
        '.git',
        '.gitignore',
        '.vscode',
        '.idea',
        '*.pyc',
        '*.pyo',
        '*.log',
        'claim_automation.log',
        'generated_images',
        'generated_orders',
        'listsp_ok.txt',
        'listsp_ng.txt',
        'listsp_data.txt',
        'config.json',  # 排除配置文件，保留示例配置
        '.python-version'
    }
    
    # 需要包含的核心文件
    include_files = [
        'main.py',
        'tasks3.py',
        'tasks2.py',
        'tasks.py',
        'auto_claim_script.py',
        'config.example.json',
        'pyproject.toml',
        'API配置说明.md',
        '项目说明.txt',
        'list01.txt'
    ]
    
    # 需要包含的目录
    include_dirs = [
        'src',
        'templates'
    ]
    
    print(f"开始打包项目...")
    print(f"包名: {package_name}")
    
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        
        # 添加核心文件
        for file_name in include_files:
            if os.path.exists(file_name):
                zipf.write(file_name, file_name)
                print(f"✅ 添加文件: {file_name}")
            else:
                print(f"⚠️ 文件不存在: {file_name}")
        
        # 添加目录
        for dir_name in include_dirs:
            if os.path.exists(dir_name):
                for root, dirs, files in os.walk(dir_name):
                    # 过滤掉排除的目录
                    dirs[:] = [d for d in dirs if d not in exclude_patterns]
                    
                    for file in files:
                        # 检查文件是否应该被排除
                        should_exclude = False
                        for pattern in exclude_patterns:
                            if pattern.startswith('*'):
                                if file.endswith(pattern[1:]):
                                    should_exclude = True
                                    break
                            elif pattern in file or pattern in root:
                                should_exclude = True
                                break
                        
                        if not should_exclude:
                            file_path = os.path.join(root, file)
                            arcname = file_path.replace('\\', '/')
                            zipf.write(file_path, arcname)
                            print(f"✅ 添加文件: {arcname}")
                
                print(f"✅ 添加目录: {dir_name}")
            else:
                print(f"⚠️ 目录不存在: {dir_name}")
        
        # 创建README文件
        readme_content = f"""# 索赔自动化系统 v{timestamp}

## 项目说明
这是一个自动化索赔系统，用于处理Poppi产品的索赔申请。

## 主要文件说明
- main.py: 主程序入口，负责系统管理和任务调度
- tasks3.py: 具体的自动化任务执行模块
- src/: 源代码目录，包含各种功能模块
- config.example.json: 配置文件示例
- list01.txt: 输入数据文件

## 使用方法
1. 复制 config.example.json 为 config.json 并配置相关参数
2. 在 list01.txt 中添加待处理的数据
3. 运行 python main.py 启动程序

## 最新修改
- 修复了VNB号码获取逻辑，只有获取到有效VNB号码才算成功
- 改进了错误处理，未获取到VNB号码的情况会写入失败文件
- 优化了Cloudflare验证处理

## 打包时间
{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
        
        zipf.writestr('README.md', readme_content)
        print("✅ 添加README.md")
    
    print(f"\n🎉 项目打包完成!")
    print(f"📦 包文件: {package_name}")
    print(f"📊 文件大小: {os.path.getsize(package_name) / 1024 / 1024:.2f} MB")
    
    return package_name

if __name__ == "__main__":
    try:
        package_file = create_project_package()
        print(f"\n✅ 打包成功: {package_file}")
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
