"""
主程序入口 - 重构版本
索赔自动化系统主程序，调用 tasks3.py 执行具体的自动化任务
"""

import logging
import json
import threading
import queue
import time
import random
from src.bit_browser import BitBrowserManager
from src.config import config
from src.file_handler import file_handler
from DrissionPage import ChromiumOptions, Chromium

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('claim_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class ClaimAutomationSystem:
    """索赔自动化系统主类 - 负责系统管理和任务调度"""

    def __init__(self):
        """初始化系统"""
        self.browser_manager = BitBrowserManager()
        self.browser_ids = []

    def initialize_system(self):
        """初始化系统"""
        try:
            logger.info("初始化索赔自动化系统...")
            
            # 获取可用浏览器
            self.browser_ids = self.get_existing_browsers()
            
            if not self.browser_ids:
                logger.error("比特浏览器API连接失败，请确保比特浏览器已启动")
                return False
            
            logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False

    def get_existing_browsers(self):
        """获取现有浏览器列表"""
        try:
            result = self.browser_manager.get_browser_list()
            if result and result.get("success"):
                # 从响应中提取浏览器列表
                data = result.get("data", {})
                if isinstance(data, dict) and "list" in data:
                    browsers = data["list"]
                else:
                    browsers = data if isinstance(data, list) else []
                
                if browsers:
                    browser_ids = [browser['id'] for browser in browsers]
                    logger.info(f"获取到 {len(browser_ids)} 个现有浏览器")
                    for i, browser in enumerate(browsers, 1):
                        name = browser.get('name', '未命名')
                        browser_id = browser['id']
                        logger.info(f"  浏览器 {i}: {name} (ID: {browser_id})")
                    return browser_ids
                else:
                    logger.warning("没有找到可用的浏览器")
                    return []
            else:
                logger.warning("获取浏览器列表失败")
                return []
        except Exception as e:
            logger.error(f"获取浏览器列表失败: {e}")
            return []

    def parse_data_line(self, line):
        """解析数据行"""
        try:
            parts = line.split('----')
            if len(parts) >= 10:  # 实际有10个字段
                # 邮编自动补零到5位
                zip_code = parts[5].strip()
                if zip_code.isdigit() and len(zip_code) < 5:
                    zip_code = zip_code.zfill(5)
                
                return {
                    'first_name': parts[0].strip(),
                    'last_name': parts[1].strip(),
                    'address1': parts[2].strip(),
                    'city': parts[3].strip(),
                    'state': parts[4].strip(),
                    'zip': zip_code,
                    'phone1': parts[6].strip(),  # 第一个电话号码
                    'phone2': parts[7].strip(),  # 第二个电话号码
                    'phone3': parts[8].strip(),  # 第三个电话号码（主要电话号码）
                    'email': parts[9].strip()    # 邮箱地址
                }
            else:
                raise ValueError(f"数据格式不正确，期望10个字段，实际{len(parts)}个")
        except Exception as e:
            raise ValueError(f"解析数据失败: {e}")

    def worker_thread_loop_read_delete(self, browser_id, thread_num):
        """
        工作线程循环函数 - 真正的读一条删除一条模式
        每个线程直接从文件读取数据，读取后立即删除该行
        """
        import threading
        from tasks3 import execute_single_claim_task3_with_data_management  # 导入tasks3的读一条删除一条函数

        thread_id = threading.get_ident()
        logger.info(f"线程 {thread_num} (ID: {thread_id}) 开始工作，固定使用浏览器ID: {browser_id}")
        logger.info(f"线程 {thread_num} 使用真正的读一条删除一条模式")

        while True:
            try:
                # 直接调用读一条删除一条的函数
                result = execute_single_claim_task3_with_data_management(browser_id, thread_num)

                if not result.get("success"):
                    error = result.get("error", "")
                    if "没有更多数据" in error:
                        logger.info(f"线程 {thread_num} 完成所有任务，退出")
                        break
                    else:
                        logger.error(f"线程 {thread_num} 任务失败: {error}")

                # 等待10秒，避免过于频繁
                wait_time = 10
                logger.info(f"线程 {thread_num} 等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)

            except Exception as e:
                logger.error(f"线程 {thread_num} 处理异常: {e}")
                time.sleep(5)  # 异常后短暂等待
                continue

        logger.info(f"线程 {thread_num} 退出")

    def worker_thread_loop_queue(self, data_queue, browser_id, thread_num):
        """
        工作线程循环函数 - 队列模式（旧版本，保留兼容性）
        从队列获取数据进行处理
        """
        import threading
        from tasks3 import execute_single_claim_task3  # 导入tasks3的执行函数

        thread_id = threading.get_ident()
        logger.info(f"线程 {thread_num} (ID: {thread_id}) 开始工作，固定使用浏览器ID: {browser_id}")
        logger.info(f"线程 {thread_num} 使用队列模式（非读一条删除一条）")

        while True:
            try:
                # 从队列获取数据，超时10秒
                try:
                    row_data = data_queue.get(timeout=10)
                except queue.Empty:
                    logger.info(f"线程 {thread_num} 等待超时，检查是否还有任务...")
                    if data_queue.empty():
                        break
                    continue

                logger.info(f"线程 {thread_num} 开始处理数据: {row_data[:50]}...")

                # 解析数据
                try:
                    parsed_data = self.parse_data_line(row_data)
                    logger.info(f"线程 {thread_num} 申请人: {parsed_data['first_name']} {parsed_data['last_name']}")
                    logger.info(f"线程 {thread_num} 邮编: {parsed_data['zip']} (已补零)")
                except Exception as e:
                    logger.error(f"线程 {thread_num} 数据解析失败: {e}")
                    file_handler.write_failed_record(row_data, f"数据解析失败: {e}")
                    data_queue.task_done()
                    continue

                # 调用 tasks3.py 执行自动化填表
                result = execute_single_claim_task3(browser_id, parsed_data, thread_num)

                # 记录结果 - 修改逻辑：只有获取到有效VNB号码才算成功
                if result.get("success"):
                    claim_id = result.get("claim_id", "")
                    # 验证claim_id是否为有效的VNB号码
                    if claim_id and claim_id.strip() and "VNB" in claim_id.upper():
                        success_record = f"{row_data}----{claim_id}"
                        file_handler.write_success_record(success_record)
                        logger.info(f"线程 {thread_num} 任务完成: 成功 - Claim ID: {claim_id}")
                    else:
                        # 即使返回success=True，但没有有效VNB号码，仍然算失败
                        error_msg = f"未获取到有效VNB号码，获取到的信息: {claim_id or '无'}"
                        file_handler.write_failed_record(row_data, error_msg)
                        logger.error(f"线程 {thread_num} 任务完成: 失败 - {error_msg}")
                else:
                    error_msg = result.get("error", "未知错误")
                    file_handler.write_failed_record(row_data, error_msg)
                    logger.error(f"线程 {thread_num} 任务完成: 失败 - {error_msg}")

                # 标记任务完成
                data_queue.task_done()

                # 等待10秒，避免过于频繁
                wait_time = 10
                logger.info(f"线程 {thread_num} 等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)

            except Exception as e:
                logger.error(f"线程 {thread_num} 处理异常: {e}")
                try:
                    file_handler.write_failed_record(row_data, f"处理异常: {e}")
                    data_queue.task_done()
                except:
                    pass
                continue

        logger.info(f"线程 {thread_num} 退出")

    def run_claim_automation(self, thread_count=None):
        """运行索赔自动化（多线程多窗口模式）"""
        try:
            logger.info("开始索赔自动化流程...")

            # 检查浏览器数量
            if not self.browser_ids:
                logger.error("没有可用的浏览器窗口")
                return

            # 让用户选择线程数量
            if thread_count is None:
                max_threads = len(self.browser_ids)
                print(f"\n可用浏览器数量: {max_threads}")
                print("请输入要使用的线程数量 (直接回车使用默认1个线程):")

                while True:
                    choice = input(f"线程数量 (1-{max_threads}): ").strip()
                    
                    if choice == "":
                        thread_count = 1
                        break
                    else:
                        try:
                            thread_count = int(choice)
                            if 1 <= thread_count <= max_threads:
                                break
                            else:
                                print(f"线程数量必须在 1-{max_threads} 之间，请重新输入")
                        except ValueError:
                            print("请输入有效的数字")

            # 使用选定数量的浏览器
            selected_browsers = self.browser_ids[:thread_count]

            # 检查输入文件状态
            file_status = file_handler.get_file_status()
            input_lines = file_status['input_file']['line_count']

            if input_lines == 0:
                logger.warning("没有找到待处理的数据")
                return

            logger.info(f"待处理数据: {input_lines} 条")
            logger.info(f"使用 {thread_count} 个线程进行处理")
            logger.info("邮编将自动补零到5位")

            # 为队列模式准备数据（只在需要时读取）
            data_queue = None

            # 直接使用读一条删除一条模式
            use_read_delete_mode = True

            if use_read_delete_mode:
                logger.info("🔥 使用真正的读一条删除一条模式")
                logger.info("📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行")

                # 启动读一条删除一条模式的工作线程
                threads = []
                for i, browser_id in enumerate(selected_browsers):
                    thread = threading.Thread(
                        target=self.worker_thread_loop_read_delete,
                        args=(browser_id, i+1),
                        daemon=True,
                        name=f"ReadDeleteThread-{i+1}"
                    )
                    threads.append(thread)
                    thread.start()
                    logger.info(f"启动读一条删除一条线程 {i+1}，使用浏览器ID: {browser_id}")
                    time.sleep(2)  # 错开启动时间

                # 等待所有线程结束
                logger.info("等待所有线程完成...")
                for thread in threads:
                    thread.join()
            else:
                logger.info("⚠️ 使用队列模式（非读一条删除一条）")

                # 读取输入数据到队列
                try:
                    with open(file_handler.file_paths['input_file'], 'r', encoding='utf-8') as f:
                        data_lines = [line.strip() for line in f.readlines() if line.strip()]
                except Exception as e:
                    logger.error(f"读取输入文件失败: {e}")
                    return

                # 创建数据队列
                data_queue = queue.Queue()
                for line in data_lines:
                    data_queue.put(line.strip())

                # 启动队列模式的工作线程
                threads = []
                for i, browser_id in enumerate(selected_browsers):
                    thread = threading.Thread(
                        target=self.worker_thread_loop_queue,
                        args=(data_queue, browser_id, i+1),
                        daemon=True,
                        name=f"QueueThread-{i+1}"
                    )
                    threads.append(thread)
                    thread.start()
                    logger.info(f"启动队列线程 {i+1}，使用浏览器ID: {browser_id}")

                # 等待所有任务完成
                logger.info("等待所有任务完成...")
                data_queue.join()

                # 等待所有线程结束
                for thread in threads:
                    thread.join(timeout=5)

            logger.info("所有任务已完成")

        except KeyboardInterrupt:
            logger.info("用户中断了程序")
        except Exception as e:
            logger.error(f"自动化运行异常: {e}")

    def shutdown_system(self):
        """关闭系统"""
        try:
            logger.info("关闭索赔自动化系统...")
            logger.info("系统已关闭")
        except Exception as e:
            logger.error(f"关闭系统时出错: {e}")


def test_browser_connection():
    """测试浏览器连接功能 - 使用现有浏览器"""
    system = ClaimAutomationSystem()

    try:
        # 初始化系统
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return

        # 获取现有浏览器列表
        browsers = system.get_existing_browsers()
        if not browsers:
            print("❌ 没有找到可用的浏览器，请先在比特浏览器中创建浏览器")
            return

        # 使用第一个可用的浏览器进行测试
        test_browser_id = browsers[0]
        print(f"🔍 测试浏览器连接，使用浏览器ID: {test_browser_id}")

        # 尝试打开浏览器
        try:
            result = system.browser_manager.open_browser(test_browser_id)
            
            if result and result.get("success"):
                print("✅ 浏览器连接测试成功")
                print(f"   浏览器ID: {test_browser_id}")
                print(f"   调试端口: {result.get('debug_port')}")
                print(f"   WebSocket URL: {result.get('ws_url')}")
                print(f"   HTTP URL: {result.get('http_url')}")

                print("\n📋 DrissionPageMCP 连接配置:")
                mcp_config = {
                    "config": {
                        "debug_port": int(result.get('debug_port', 9222))
                    }
                }
                print(json.dumps(mcp_config, indent=2, ensure_ascii=False))

                input("\n按回车键继续...")
                
                # 测试完成后关闭浏览器
                try:
                    system.browser_manager.close_browser(test_browser_id)
                    print("🔒 测试浏览器已关闭")
                except:
                    pass

            else:
                error_msg = result.get('error', '未知错误') if result else '连接失败'
                print(f"❌ 浏览器连接测试失败: {error_msg}")
                
        except Exception as browser_error:
            print(f"❌ 浏览器操作失败: {browser_error}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"浏览器连接测试失败: {e}")
    finally:
        # 清理资源
        try:
            system.shutdown_system()
        except:
            pass


def run_automation():
    """运行自动化索赔"""
    system = ClaimAutomationSystem()

    try:
        # 初始化系统
        if not system.initialize_system():
            print("系统初始化失败")
            return

        # 检查是否有可用浏览器
        if not system.browser_ids:
            print("❌ 没有可用的比特浏览器窗口")
            print("请先在比特浏览器中创建一些浏览器窗口")
            return

        print(f"✅ 检测到 {len(system.browser_ids)} 个可用浏览器窗口")

        # 检查输入文件
        file_status = file_handler.get_file_status()
        input_lines = file_status['input_file']['line_count']

        if input_lines == 0:
            print("❌ 输入文件为空，请先添加数据到 list01.txt")
            return

        print(f"✅ 待处理数据: {input_lines} 条")
        print("✅ 邮编将自动补零到5位")
        print("✅ 支付方式: Venmo (双邮箱确认)")
        print("✅ 默认线程数: 1 (可在运行时选择)")

        # 运行自动化
        system.run_claim_automation()

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了程序")
    except Exception as e:
        print(f"❌ 运行异常: {e}")
        logger.error(f"自动化运行异常: {e}")
    finally:
        # 清理资源
        system.shutdown_system()


def main():
    """主函数"""
    print("索赔自动化系统")
    print("=" * 50)

    # 检查配置
    if not config.validate_config():
        print("配置不完整，请先设置配置")
        config.update_from_user_input()

    # 显示文件状态
    file_status = file_handler.get_file_status()
    print(f"\n文件状态:")
    print(f"输入文件: {file_status['input_file']['line_count']} 行")
    print(f"成功记录: {file_status['success_file']['line_count']} 条")
    print(f"失败记录: {file_status['failed_file']['line_count']} 条")

    while True:
        print(f"\n请选择操作:")
        print("1. 测试浏览器连接")
        print("2. 运行自动化索赔")
        print("3. 配置设置")
        print("4. 文件状态")
        print("5. 退出")

        choice = input("请输入选择 (1-5): ").strip()

        if choice == "1":
            test_browser_connection()
        elif choice == "2":
            run_automation()
        elif choice == "3":
            config.update_from_user_input()
        elif choice == "4":
            file_status = file_handler.get_file_status()
            print(f"\n文件状态:")
            print(f"输入文件: {file_status['input_file']['line_count']} 行")
            print(f"成功记录: {file_status['success_file']['line_count']} 条")
            print(f"失败记录: {file_status['failed_file']['line_count']} 条")
        elif choice == "5":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
