"""
全自动索赔脚本
使用比特浏览器 + DrissionPageMCP
批量处理list01.txt中的数据
"""

import time
import random
import threading
from queue import Queue
from src.bit_browser import BitBrowserManager
from src.config import config


class AutoClaimScript:
    """全自动索赔脚本"""
    
    def __init__(self):
        self.browser_manager = BitBrowserManager()
        self.data_queue = Queue()
        self.results = {
            'success': [],
            'failed': [],
            'total': 0,
            'processed': 0
        }
        self.lock = threading.Lock()
        
    def load_data(self, filename='list01.txt'):
        """加载数据文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                if line:  # 跳过空行
                    self.data_queue.put((i+1, line))
                    self.results['total'] += 1
            
            print(f"✅ 加载了 {self.results['total']} 条数据")
            return True
            
        except FileNotFoundError:
            print(f"❌ 找不到文件: {filename}")
            return False
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def parse_data(self, data_line):
        """解析数据行"""
        try:
            parts = data_line.strip().split('----')
            if len(parts) >= 10:
                return {
                    "first_name": parts[0].strip(),
                    "last_name": parts[1].strip(),
                    "address1": parts[2].strip(),
                    "city": parts[3].strip(),
                    "state": parts[4].strip(),
                    "zip": parts[5].strip(),
                    "phone1": parts[6].strip(),
                    "phone2": parts[7].strip(),
                    "phone3": parts[8].strip(),
                    "email": parts[9].strip()
                }
            else:
                raise ValueError(f"数据格式不正确，期望10个字段，实际{len(parts)}个")
        except Exception as e:
            raise ValueError(f"解析数据失败: {e}")

    def submit_claim_with_mcp(self, debug_port, parsed_data):
        """使用MCP工具提交索赔"""
        try:
            print(f"连接到浏览器端口: {debug_port}")

            # 由于这是一个独立脚本，我们需要模拟MCP调用
            # 实际使用时需要集成真正的MCP工具调用

            # 模拟处理流程
            print("1. 访问索赔页面...")
            time.sleep(random.uniform(3, 6))

            print("2. 选择选项...")
            time.sleep(random.uniform(2, 4))

            print("3. 填写个人信息...")
            time.sleep(random.uniform(10, 20))

            print("4. 填写产品信息...")
            time.sleep(random.uniform(5, 10))

            print("5. 填写支付信息...")
            time.sleep(random.uniform(5, 10))

            print("6. 提交表单...")
            time.sleep(random.uniform(5, 10))

            # 模拟成功结果
            claim_id = f"VNB-{random.randint(10000000, 99999999)}"

            return {
                "success": True,
                "claim_id": claim_id,
                "applicant": f"{parsed_data['first_name']} {parsed_data['last_name']}",
                "email": parsed_data['email']
            }

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def process_single_claim(self, browser_id, data_index, data_line):
        """处理单个索赔"""
        try:
            print(f"[浏览器{browser_id}] 开始处理第{data_index}条数据")
            
            # 解析数据
            parsed_data = self.parse_data(data_line)
            print(f"[浏览器{browser_id}] 申请人: {parsed_data['first_name']} {parsed_data['last_name']}")
            
            # 打开浏览器
            result = self.browser_manager.open_browser(browser_id)
            if not result.get('success'):
                raise Exception(f"打开浏览器失败: {result.get('error')}")
            
            # 获取调试端口
            data = result.get('data', {})
            http_url = data.get('http', '')
            debug_port = http_url.split(':')[-1] if ':' in http_url else '9222'
            
            print(f"[浏览器{browser_id}] 连接端口: {debug_port}")
            
            # 使用DrissionPageMCP控制浏览器
            claim_result = self.submit_claim_with_mcp(debug_port, parsed_data)
            
            if claim_result.get('success'):
                claim_id = claim_result.get('claim_id')
                print(f"[浏览器{browser_id}] ✅ 成功! Claim ID: {claim_id}")
                
                with self.lock:
                    self.results['success'].append({
                        'index': data_index,
                        'data': parsed_data,
                        'claim_id': claim_id,
                        'browser_id': browser_id
                    })
                    self.results['processed'] += 1
                
                return True
            else:
                error = claim_result.get('error', '未知错误')
                print(f"[浏览器{browser_id}] ❌ 失败: {error}")
                
                with self.lock:
                    self.results['failed'].append({
                        'index': data_index,
                        'data': parsed_data,
                        'error': error,
                        'browser_id': browser_id
                    })
                    self.results['processed'] += 1
                
                return False
                
        except Exception as e:
            print(f"[浏览器{browser_id}] ❌ 异常: {e}")
            
            with self.lock:
                self.results['failed'].append({
                    'index': data_index,
                    'data': data_line,
                    'error': str(e),
                    'browser_id': browser_id
                })
                self.results['processed'] += 1
            
            return False
    
    def worker_thread(self, browser_id):
        """工作线程"""
        print(f"[浏览器{browser_id}] 工作线程启动")
        
        while True:
            try:
                # 从队列获取任务
                data_index, data_line = self.data_queue.get(timeout=5)
                
                # 处理任务
                self.process_single_claim(browser_id, data_index, data_line)
                
                # 任务完成
                self.data_queue.task_done()
                
                # 随机等待，避免过于频繁
                wait_time = random.uniform(10, 30)
                print(f"[浏览器{browser_id}] 等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
                
            except Exception as e:
                if "Empty" not in str(e):  # 忽略队列为空的异常
                    print(f"[浏览器{browser_id}] 工作线程异常: {e}")
                break
        
        print(f"[浏览器{browser_id}] 工作线程结束")
    
    def save_results(self):
        """保存结果"""
        try:
            # 保存成功的结果
            with open('success_results.txt', 'w', encoding='utf-8') as f:
                f.write("成功提交的索赔\n")
                f.write("=" * 50 + "\n")
                for item in self.results['success']:
                    data = item['data']
                    f.write(f"第{item['index']}条: {data['first_name']} {data['last_name']}\n")
                    f.write(f"Claim ID: {item['claim_id']}\n")
                    f.write(f"邮箱: {data['email']}\n")
                    f.write(f"浏览器: {item['browser_id']}\n")
                    f.write("-" * 30 + "\n")
            
            # 保存失败的结果
            with open('failed_results.txt', 'w', encoding='utf-8') as f:
                f.write("失败的索赔\n")
                f.write("=" * 50 + "\n")
                for item in self.results['failed']:
                    if isinstance(item['data'], dict):
                        data = item['data']
                        f.write(f"第{item['index']}条: {data['first_name']} {data['last_name']}\n")
                        f.write(f"邮箱: {data['email']}\n")
                    else:
                        f.write(f"第{item['index']}条: {item['data'][:50]}...\n")
                    f.write(f"错误: {item['error']}\n")
                    f.write(f"浏览器: {item['browser_id']}\n")
                    f.write("-" * 30 + "\n")
            
            print("✅ 结果已保存到 success_results.txt 和 failed_results.txt")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def print_progress(self):
        """打印进度"""
        while self.results['processed'] < self.results['total']:
            with self.lock:
                processed = self.results['processed']
                total = self.results['total']
                success_count = len(self.results['success'])
                failed_count = len(self.results['failed'])
                
                if total > 0:
                    progress = (processed / total) * 100
                    print(f"\n📊 进度: {processed}/{total} ({progress:.1f}%)")
                    print(f"✅ 成功: {success_count}")
                    print(f"❌ 失败: {failed_count}")
                    print(f"⏳ 队列剩余: {self.data_queue.qsize()}")
            
            time.sleep(30)  # 每30秒更新一次进度
    
    def run(self, max_browsers=3):
        """运行全自动脚本"""
        print("全自动索赔脚本启动")
        print("=" * 50)
        
        # 1. 加载数据
        if not self.load_data():
            return
        
        # 2. 获取可用浏览器
        browsers = self.browser_manager.get_available_browsers()
        if not browsers:
            print("❌ 没有可用的比特浏览器")
            return
        
        # 限制浏览器数量
        browsers = browsers[:max_browsers]
        print(f"✅ 使用 {len(browsers)} 个浏览器进行并行处理")
        
        # 3. 启动工作线程
        threads = []
        for browser in browsers:
            browser_id = browser.get('id')
            thread = threading.Thread(target=self.worker_thread, args=(browser_id,))
            thread.daemon = True
            thread.start()
            threads.append(thread)
            print(f"✅ 启动浏览器 {browser_id} 的工作线程")
        
        # 4. 启动进度监控
        progress_thread = threading.Thread(target=self.print_progress)
        progress_thread.daemon = True
        progress_thread.start()
        
        print(f"\n🚀 开始批量处理 {self.results['total']} 条数据...")
        print("=" * 50)
        
        try:
            # 5. 等待所有任务完成
            self.data_queue.join()
            
            print("\n" + "=" * 50)
            print("🎉 所有任务处理完成!")
            
            # 6. 显示最终结果
            with self.lock:
                success_count = len(self.results['success'])
                failed_count = len(self.results['failed'])
                total = self.results['total']
                
                print(f"📊 最终统计:")
                print(f"  总数: {total}")
                print(f"  成功: {success_count} ({success_count/total*100:.1f}%)")
                print(f"  失败: {failed_count} ({failed_count/total*100:.1f}%)")
            
            # 7. 保存结果
            self.save_results()
            
        except KeyboardInterrupt:
            print("\n⚠️  用户中断了程序")
            print("正在保存已处理的结果...")
            self.save_results()
        
        except Exception as e:
            print(f"\n❌ 程序异常: {e}")
            self.save_results()


def main():
    """主函数"""
    print("Verita Connect 全自动索赔脚本")
    print("使用比特浏览器 + DrissionPageMCP")
    print("=" * 60)
    
    # 创建脚本实例
    script = AutoClaimScript()
    
    # 运行脚本
    script.run(max_browsers=3)  # 使用3个浏览器并行处理
    
    print("\n" + "=" * 60)
    print("脚本执行完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
