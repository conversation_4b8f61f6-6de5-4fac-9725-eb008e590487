#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE打包脚本 - 适用于UV环境
使用PyInstaller将项目打包成独立的EXE文件
"""

import os
import sys
import subprocess
import shutil
import datetime
from pathlib import Path

def check_uv_environment():
    """检查UV环境"""
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ UV环境检测成功: {result.stdout.strip()}")
            return True
        else:
            print("❌ UV环境检测失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到UV命令，请确保UV已正确安装")
        return False

def install_dependencies():
    """安装依赖项"""
    print("📦 检查项目依赖...")

    # 使用UV pip安装依赖
    try:
        subprocess.run(['uv', 'pip', 'install', 'DrissionPage', 'requests', 'pyinstaller'], check=True)
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('templates', 'templates'),
        ('config.example.json', '.'),
        ('list01.txt', '.'),
        ('API配置说明.md', '.'),
        ('项目说明.txt', '.'),
    ],
    hiddenimports=[
        'DrissionPage',
        'DrissionPage.ChromiumOptions',
        'DrissionPage.Chromium',
        'requests',
        'json',
        'threading',
        'queue',
        'time',
        'random',
        'logging',
        're',
        'os',
        'pathlib',
        'src.bit_browser',
        'src.config',
        'src.file_handler',
        'src.captcha_solver',
        'src.cloudflare_bypass',
        'src.drission_controller',
        'src.email_code_manager',
        'src.network_analyzer',
        'src.phone_manager',
        'src.thread_manager',
        'src.verification_handler',
        'tasks3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='索赔自动化系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''

    with open('main.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ 创建PyInstaller spec文件完成")

def build_exe():
    """构建EXE文件"""
    print("🔨 开始构建EXE文件...")
    
    try:
        # 使用UV运行PyInstaller
        cmd = ['uv', 'run', 'pyinstaller', '--clean', 'main.spec']
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ EXE构建完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ EXE构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_distribution_package():
    """创建分发包"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    dist_dir = f"索赔自动化系统_v{timestamp}"
    
    print(f"📦 创建分发包: {dist_dir}")
    
    # 创建分发目录
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    
    # 复制EXE文件
    exe_source = "dist/索赔自动化系统.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, os.path.join(dist_dir, "索赔自动化系统.exe"))
        print("✅ 复制EXE文件完成")
    else:
        print("❌ 未找到EXE文件")
        return False
    
    # 复制必要的配置文件
    files_to_copy = [
        "config.example.json",
        "list01.txt",
        "API配置说明.md",
        "项目说明.txt"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
            print(f"✅ 复制文件: {file_name}")
    
    # 创建使用说明
    readme_content = f"""# 索赔自动化系统 v{timestamp}

## 使用说明

### 1. 首次使用配置
1. 将 `config.example.json` 复制为 `config.json`
2. 编辑 `config.json` 文件，填入相关API配置
3. 确保比特浏览器已启动并运行在默认端口(54345)

### 2. 准备数据
1. 编辑 `list01.txt` 文件
2. 按照格式添加待处理的数据，每行一条记录
3. 数据格式：姓名----地址----城市----州----邮编----电话----邮箱等

### 3. 运行程序
1. 双击 `索赔自动化系统.exe` 启动程序
2. 根据菜单提示选择相应操作
3. 选择"运行自动化索赔"开始处理

### 4. 查看结果
- 成功记录会保存在 `listsp_ok.txt` 文件中
- 失败记录会保存在 `listsp_ng.txt` 文件中
- 程序运行日志会保存在 `claim_automation.log` 文件中

### 5. 注意事项
- 确保网络连接正常
- 确保比特浏览器正常运行
- 建议先进行浏览器连接测试
- 程序运行过程中请勿关闭比特浏览器

### 6. 技术支持
如遇问题，请查看日志文件或联系技术支持。

构建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    with open(os.path.join(dist_dir, "使用说明.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建使用说明完成")
    
    # 创建ZIP包
    zip_name = f"{dist_dir}.zip"
    shutil.make_archive(dist_dir, 'zip', dist_dir)
    
    print(f"✅ 创建ZIP分发包完成: {zip_name}")
    
    # 显示文件大小
    exe_size = os.path.getsize(os.path.join(dist_dir, "索赔自动化系统.exe")) / 1024 / 1024
    zip_size = os.path.getsize(zip_name) / 1024 / 1024
    
    print(f"📊 EXE文件大小: {exe_size:.2f} MB")
    print(f"📊 ZIP包大小: {zip_size:.2f} MB")
    
    return dist_dir, zip_name

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 索赔自动化系统 EXE 打包工具")
    print("=" * 60)
    
    # 检查UV环境
    if not check_uv_environment():
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 创建spec文件
    create_spec_file()
    
    # 构建EXE
    if not build_exe():
        return
    
    # 创建分发包
    dist_dir, zip_name = create_distribution_package()
    
    print("\n" + "=" * 60)
    print("🎉 EXE打包完成!")
    print("=" * 60)
    print(f"📁 分发目录: {dist_dir}")
    print(f"📦 ZIP包: {zip_name}")
    print("\n✅ 可以将ZIP包分发给其他用户使用")
    print("⚠️ 用户需要确保目标机器上已安装比特浏览器")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了打包过程")
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
