#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试填表操作 - 找到正确的选择器
"""

import time
import requests
import json
from DrissionPage import ChromiumOptions, Chromium

def connect_bit_browser():
    """连接比特浏览器"""
    try:
        print("检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
        print("比特浏览器服务连接正常")
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("没有找到浏览器，请先创建浏览器")
            return None, None
        
        # 使用第一个浏览器
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        if open_response.status_code != 200:
            print(f"打开浏览器失败: {open_response.text}")
            return None, None
        
        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("打开浏览器失败: 响应中没有data字段")
            return None, None
        
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        print(f"获取到调试端口信息: {http}")
        
        # 等待浏览器启动
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接比特浏览器失败: {e}")
        return None, None

def debug_page_elements(tab):
    """调试页面元素"""
    print("=" * 60)
    print("开始调试页面元素")
    print("=" * 60)
    
    try:
        print(f"当前页面标题: {tab.title}")
        print(f"当前页面URL: {tab.url}")
        
        # 检查页面是否加载完成
        print("\n1. 检查页面加载状态...")
        page_html = tab.html.lower()
        if "claim" in page_html or "poppi" in page_html:
            print("✅ 页面包含预期内容")
        else:
            print("⚠️ 页面可能未正确加载")
        
        # 检查单选框
        print("\n2. 检查单选框...")
        radio_buttons = tab.eles('x://input[@type="radio"]')
        print(f"找到 {len(radio_buttons)} 个单选框")
        
        for i, radio in enumerate(radio_buttons):
            try:
                value = radio.attr('value') or '无value'
                name = radio.attr('name') or '无name'
                id_attr = radio.attr('id') or '无id'
                print(f"  单选框 {i+1}: value='{value}', name='{name}', id='{id_attr}'")
            except Exception as e:
                print(f"  单选框 {i+1}: 获取属性失败 - {e}")
        
        # 检查包含特定文本的元素
        print("\n3. 检查包含'Claim ID'的文本...")
        claim_elements = tab.eles('x://*[contains(text(), "Claim ID")]')
        print(f"找到 {len(claim_elements)} 个包含'Claim ID'的元素")
        
        for i, elem in enumerate(claim_elements[:3]):  # 只显示前3个
            try:
                text = elem.text.strip()[:100]  # 限制长度
                tag = elem.tag
                print(f"  元素 {i+1}: <{tag}> {text}")
            except Exception as e:
                print(f"  元素 {i+1}: 获取文本失败 - {e}")
        
        # 检查表单输入框
        print("\n4. 检查表单输入框...")
        input_elements = tab.eles('x://input[@type="text"]')
        print(f"找到 {len(input_elements)} 个文本输入框")
        
        for i, input_elem in enumerate(input_elements[:5]):  # 只显示前5个
            try:
                name = input_elem.attr('name') or '无name'
                id_attr = input_elem.attr('id') or '无id'
                placeholder = input_elem.attr('placeholder') or '无placeholder'
                print(f"  输入框 {i+1}: name='{name}', id='{id_attr}', placeholder='{placeholder}'")
            except Exception as e:
                print(f"  输入框 {i+1}: 获取属性失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"调试页面元素失败: {e}")
        return False

def test_form_filling(tab):
    """测试表单填写"""
    print("=" * 60)
    print("开始测试表单填写")
    print("=" * 60)
    
    try:
        # 测试数据
        test_data = {
            'first_name': 'Christine',
            'last_name': 'McIntosh',
            'address1': '665 PRIMROSE ST',
            'city': 'HAVERHILL',
            'zip': '01830'
        }
        
        print("测试数据:", test_data)
        
        # 先轻微向下滚动，找到表单区域
        print("\n1. 轻微向下滚动找到表单...")
        tab.scroll.down(3)  # 轻微滚动
        time.sleep(2)
        
        # 查找First Name输入框
        print("\n2. 查找First Name输入框...")
        first_name_selectors = [
            'x://input[@name="firstName"]',
            'x://input[contains(@name, "first")]',
            'x://input[contains(@id, "first")]',
            'x://input[contains(@placeholder, "First")]',
            'x://input[contains(@placeholder, "first")]'
        ]
        
        first_name_input = None
        for selector in first_name_selectors:
            try:
                input_elem = tab.ele(selector, timeout=2)
                if input_elem:
                    print(f"✅ 找到First Name输入框: {selector}")
                    first_name_input = input_elem
                    break
            except:
                continue
        
        if not first_name_input:
            print("❌ 未找到First Name输入框")
            # 尝试查找所有可能的输入框
            print("查找所有文本输入框...")
            all_inputs = tab.eles('x://input[@type="text"]')
            for i, inp in enumerate(all_inputs):
                try:
                    name = inp.attr('name') or '无name'
                    id_attr = inp.attr('id') or '无id'
                    placeholder = inp.attr('placeholder') or '无placeholder'
                    print(f"  输入框 {i+1}: name='{name}', id='{id_attr}', placeholder='{placeholder}'")
                except:
                    pass
            return False
        
        # 尝试填写First Name
        print(f"\n3. 尝试填写First Name: {test_data['first_name']}")
        try:
            # 滚动到元素位置
            first_name_input.scroll.to_see()
            time.sleep(1)
            
            # 清空并输入
            first_name_input.clear()
            first_name_input.input(test_data['first_name'])
            print("✅ First Name填写成功")
            
            # 验证输入
            current_value = first_name_input.value
            print(f"当前值: '{current_value}'")
            
        except Exception as e:
            print(f"❌ First Name填写失败: {e}")
            return False
        
        # 查找Last Name输入框
        print("\n4. 查找Last Name输入框...")
        last_name_selectors = [
            'x://input[@name="lastName"]',
            'x://input[contains(@name, "last")]',
            'x://input[contains(@id, "last")]',
            'x://input[contains(@placeholder, "Last")]',
            'x://input[contains(@placeholder, "last")]'
        ]
        
        last_name_input = None
        for selector in last_name_selectors:
            try:
                input_elem = tab.ele(selector, timeout=2)
                if input_elem:
                    print(f"✅ 找到Last Name输入框: {selector}")
                    last_name_input = input_elem
                    break
            except:
                continue
        
        if last_name_input:
            try:
                last_name_input.clear()
                last_name_input.input(test_data['last_name'])
                print("✅ Last Name填写成功")
            except Exception as e:
                print(f"❌ Last Name填写失败: {e}")
        else:
            print("❌ 未找到Last Name输入框")
        
        return True
        
    except Exception as e:
        print(f"测试表单填写失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print("开始调试填表操作...")
        
        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print("❌ 连接比特浏览器失败")
            return
        
        print("✅ 成功连接比特浏览器")
        
        # 打开项目网址
        print("打开项目网址...")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(8)
        
        # 调试页面元素
        debug_page_elements(tab)
        
        # 测试表单填写
        test_form_filling(tab)
        
        # 保持浏览器打开以便观察
        print("\n保持浏览器打开60秒以便观察...")
        time.sleep(60)
        
    except Exception as e:
        print(f"调试过程中出现异常: {e}")
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

if __name__ == "__main__":
    main()
