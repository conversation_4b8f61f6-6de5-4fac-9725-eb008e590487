#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
记录CF相关元素，遍历Success状态和可点击的验证元素
"""

import time
import requests
import json
from DrissionPage import ChromiumOptions, Chromium

def connect_bit_browser():
    """连接比特浏览器"""
    try:
        print("连接比特浏览器...")
        headers = {'Content-Type': 'application/json'}
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("❌ 没有找到浏览器")
            return None, None
        
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        open_response_json = open_response.json()
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接失败: {e}")
        return None, None

def record_success_elements(tab):
    """记录Success相关元素"""
    print("=" * 60)
    print("记录Success相关元素")
    print("=" * 60)
    
    success_records = []
    
    try:
        # 查找包含Success的所有元素
        success_selectors = [
            'x://*[contains(text(), "Success")]',
            'x://*[contains(text(), "SUCCESS")]',
            'x://*[contains(text(), "success")]',
            'x://*[contains(@class, "success")]',
            'x://*[contains(@id, "success")]',
            'x://*[text()="✓"]',
            'x://*[contains(text(), "✓")]',
            'x://*[contains(text(), "Verified")]',
            'x://*[contains(text(), "Complete")]'
        ]
        
        for i, selector in enumerate(success_selectors):
            try:
                elements = tab.eles(selector)
                if elements:
                    print(f"\n选择器 {i+1}: {selector}")
                    print(f"找到 {len(elements)} 个元素")
                    
                    for j, elem in enumerate(elements):
                        try:
                            record = {
                                'selector': selector,
                                'index': j,
                                'tag': elem.tag,
                                'text': elem.text.strip()[:100],
                                'id': elem.attr('id') or '',
                                'class': elem.attr('class') or '',
                                'style': elem.attr('style') or '',
                                'visible': elem.is_displayed(),
                                'xpath': f"{selector}[{j+1}]"
                            }
                            
                            success_records.append(record)
                            
                            print(f"  元素 {j+1}:")
                            print(f"    标签: {record['tag']}")
                            print(f"    文本: {record['text']}")
                            print(f"    ID: {record['id']}")
                            print(f"    Class: {record['class'][:50]}")
                            print(f"    可见: {record['visible']}")
                            print(f"    XPath: {record['xpath']}")
                            
                        except Exception as e:
                            print(f"  元素 {j+1}: 获取信息失败 - {e}")
                            
            except Exception as e:
                print(f"选择器 {i+1} 失败: {e}")
    
    except Exception as e:
        print(f"记录Success元素失败: {e}")
    
    return success_records

def record_clickable_cf_elements(tab):
    """记录可点击的CF验证元素"""
    print("=" * 60)
    print("记录可点击的CF验证元素")
    print("=" * 60)
    
    clickable_records = []
    
    try:
        # CF验证相关的可点击元素选择器
        clickable_selectors = [
            # 复选框
            'x://input[@type="checkbox"]',
            # CF相关div（可能可点击）
            'x://div[contains(@class, "cf-")]',
            'x://div[contains(@class, "turnstile")]',
            'x://div[contains(@class, "challenge")]',
            'x://div[contains(@id, "cf-")]',
            'x://div[contains(@id, "turnstile")]',
            # 按钮
            'x://button[contains(@class, "cf-")]',
            'x://button[contains(text(), "Verify")]',
            'x://button[contains(text(), "Continue")]',
            # 可点击的span或div
            'x://*[@role="button"]',
            'x://*[@onclick]',
            # iframe（CF验证通常在iframe中）
            'x://iframe',
            # 特定的CF验证框
            'x://div[@data-sitekey]',
            'x://*[contains(@class, "widget")]'
        ]
        
        for i, selector in enumerate(clickable_selectors):
            try:
                elements = tab.eles(selector)
                if elements:
                    print(f"\n选择器 {i+1}: {selector}")
                    print(f"找到 {len(elements)} 个元素")
                    
                    for j, elem in enumerate(elements):
                        try:
                            # 检查元素是否可能与CF相关
                            tag = elem.tag
                            text = elem.text.strip()[:100]
                            id_attr = elem.attr('id') or ''
                            class_attr = elem.attr('class') or ''
                            onclick = elem.attr('onclick') or ''
                            role = elem.attr('role') or ''
                            data_sitekey = elem.attr('data-sitekey') or ''
                            src = elem.attr('src') or '' if tag == 'iframe' else ''
                            
                            # 判断是否是CF相关元素
                            cf_indicators = [
                                'cf-' in id_attr.lower(),
                                'cf-' in class_attr.lower(),
                                'turnstile' in class_attr.lower(),
                                'challenge' in class_attr.lower(),
                                'cloudflare' in src.lower(),
                                'turnstile' in src.lower(),
                                data_sitekey != '',
                                'verify' in text.lower(),
                                'human' in text.lower()
                            ]
                            
                            is_cf_related = any(cf_indicators)
                            
                            record = {
                                'selector': selector,
                                'index': j,
                                'tag': tag,
                                'text': text,
                                'id': id_attr,
                                'class': class_attr,
                                'onclick': onclick,
                                'role': role,
                                'data_sitekey': data_sitekey,
                                'src': src,
                                'visible': elem.is_displayed(),
                                'enabled': elem.is_enabled() if hasattr(elem, 'is_enabled') else True,
                                'cf_related': is_cf_related,
                                'xpath': f"{selector}[{j+1}]"
                            }
                            
                            clickable_records.append(record)
                            
                            status = "🛡️ CF相关" if is_cf_related else "📄 普通"
                            print(f"  元素 {j+1}: {status}")
                            print(f"    标签: {tag}")
                            print(f"    文本: {text}")
                            print(f"    ID: {id_attr}")
                            print(f"    Class: {class_attr[:50]}")
                            if src:
                                print(f"    Src: {src[:50]}")
                            if data_sitekey:
                                print(f"    Data-sitekey: {data_sitekey}")
                            print(f"    可见: {record['visible']}")
                            print(f"    可用: {record['enabled']}")
                            print(f"    XPath: {record['xpath']}")
                            
                        except Exception as e:
                            print(f"  元素 {j+1}: 获取信息失败 - {e}")
                            
            except Exception as e:
                print(f"选择器 {i+1} 失败: {e}")
    
    except Exception as e:
        print(f"记录可点击元素失败: {e}")
    
    return clickable_records

def record_page_state_changes(tab, duration=30):
    """记录页面状态变化"""
    print("=" * 60)
    print(f"记录页面状态变化 (监控{duration}秒)")
    print("=" * 60)
    
    states = []
    
    try:
        start_time = time.time()
        check_interval = 2  # 每2秒检查一次
        
        while time.time() - start_time < duration:
            current_time = time.time() - start_time
            
            # 记录当前状态
            state = {
                'time': round(current_time, 1),
                'title': tab.title,
                'url': tab.url,
                'success_count': len(tab.eles('x://*[contains(text(), "Success")]')),
                'radio_count': len(tab.eles('x://input[@type="radio"]')),
                'button_count': len(tab.eles('x://button')),
                'iframe_count': len(tab.eles('x://iframe')),
                'cf_div_count': len(tab.eles('x://div[contains(@class, "cf-")]'))
            }
            
            states.append(state)
            
            print(f"时间 {state['time']}s:")
            print(f"  标题: {state['title']}")
            print(f"  Success元素: {state['success_count']} 个")
            print(f"  单选框: {state['radio_count']} 个")
            print(f"  按钮: {state['button_count']} 个")
            print(f"  iframe: {state['iframe_count']} 个")
            print(f"  CF div: {state['cf_div_count']} 个")
            
            time.sleep(check_interval)
    
    except Exception as e:
        print(f"记录状态变化失败: {e}")
    
    return states

def main():
    """主函数"""
    try:
        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print("❌ 连接比特浏览器失败")
            return
        
        print("✅ 成功连接比特浏览器")
        
        # 导航到目标页面
        print("导航到目标页面...")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")
        time.sleep(5)
        
        print(f"当前URL: {tab.url}")
        print(f"当前标题: {tab.title}")
        
        # 记录Success元素
        success_records = record_success_elements(tab)
        
        # 记录可点击的CF元素
        clickable_records = record_clickable_cf_elements(tab)
        
        # 记录页面状态变化
        state_records = record_page_state_changes(tab, 20)
        
        # 输出总结
        print("\n" + "=" * 60)
        print("记录总结")
        print("=" * 60)
        
        print(f"Success相关元素: {len(success_records)} 个")
        cf_related_count = sum(1 for r in clickable_records if r['cf_related'])
        print(f"可点击元素总数: {len(clickable_records)} 个")
        print(f"CF相关可点击元素: {cf_related_count} 个")
        print(f"状态变化记录: {len(state_records)} 个时间点")
        
        # 重点关注的元素
        print("\n重点关注的元素:")
        for record in clickable_records:
            if record['cf_related'] and record['visible']:
                print(f"🎯 {record['tag']} - {record['text'][:30]} - {record['xpath']}")
        
        for record in success_records:
            if record['visible'] and 'success' in record['text'].lower():
                print(f"✅ {record['tag']} - {record['text'][:30]} - {record['xpath']}")
        
        # 保持浏览器打开
        print(f"\n保持浏览器打开60秒以便观察...")
        time.sleep(60)
        
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

if __name__ == "__main__":
    main()
