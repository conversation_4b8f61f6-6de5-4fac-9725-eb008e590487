#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DrssionPageMCP连接比特浏览器的专用脚本
"""

import time
import requests
import json
from DrissionPage import ChromiumOptions, Chromium

def connect_bit_browser_for_mcp():
    """连接比特浏览器并返回调试端口信息，供DrssionPageMCP使用"""
    try:
        print("=" * 60)
        print("DrssionPageMCP连接比特浏览器")
        print("=" * 60)
        
        # 检查比特浏览器服务
        print("1. 检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
        print("✅ 比特浏览器服务连接正常")
        
        # 获取浏览器列表
        print("2. 获取浏览器列表...")
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("❌ 没有找到浏览器，请先创建浏览器")
            return None
        
        print(f"找到 {len(browsers)} 个浏览器")
        
        # 使用第一个浏览器
        browser_id = browsers[0]['id']
        browser_name = browsers[0].get('name', '未知')
        print(f"3. 使用浏览器: {browser_name} (ID: {browser_id})")
        
        # 打开浏览器
        print("4. 打开浏览器...")
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        if open_response.status_code != 200:
            print(f"❌ 打开浏览器失败: {open_response.text}")
            return None
        
        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("❌ 打开浏览器失败: 响应中没有data字段")
            return None
        
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        
        print(f"✅ 浏览器打开成功")
        print(f"   驱动路径: {driver}")
        print(f"   调试地址: {http}")
        
        # 解析调试端口
        debug_port = http.split(':')[-1]
        print(f"   调试端口: {debug_port}")
        
        # 等待浏览器启动
        print("5. 等待浏览器启动...")
        time.sleep(3)
        
        # 使用DrissionPage连接验证
        print("6. 验证DrissionPage连接...")
        try:
            co = ChromiumOptions()
            co.set_browser_path(driver)
            co.set_address(http)
            
            browser = Chromium(co)
            tab = browser.latest_tab
            
            # 测试连接
            current_url = tab.url
            current_title = tab.title
            
            print(f"✅ DrissionPage连接成功")
            print(f"   当前URL: {current_url}")
            print(f"   当前标题: {current_title}")
            
            # 返回连接信息
            connection_info = {
                'success': True,
                'browser_id': browser_id,
                'browser_name': browser_name,
                'debug_port': debug_port,
                'debug_address': http,
                'driver_path': driver,
                'browser_object': browser,
                'tab_object': tab
            }
            
            print("\n" + "=" * 60)
            print("连接信息总结:")
            print(f"浏览器ID: {browser_id}")
            print(f"浏览器名称: {browser_name}")
            print(f"调试端口: {debug_port}")
            print(f"调试地址: {http}")
            print("=" * 60)
            
            return connection_info
            
        except Exception as e:
            print(f"❌ DrissionPage连接验证失败: {e}")
            return None
        
    except Exception as e:
        print(f"❌ 连接比特浏览器失败: {e}")
        return None

def navigate_to_target_page(tab):
    """导航到目标页面"""
    try:
        print("\n" + "=" * 60)
        print("导航到目标页面")
        print("=" * 60)
        
        target_url = "https://veritaconnect.com/poppisettlement/Claimant"
        print(f"目标URL: {target_url}")
        
        print("正在导航...")
        tab.get(target_url)
        
        # 等待页面开始加载
        time.sleep(2)
        
        print(f"✅ 导航完成")
        print(f"当前URL: {tab.url}")
        print(f"当前标题: {tab.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导航失败: {e}")
        return False

def connect_existing_bit_browser():
    """连接已经打开的比特浏览器（不重新打开）"""
    try:
        print("=" * 60)
        print("连接已经打开的比特浏览器")
        print("=" * 60)

        # 检查比特浏览器服务
        print("1. 检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
        print("✅ 比特浏览器服务连接正常")

        # 获取浏览器列表
        print("2. 获取浏览器列表...")
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list",
                               data=json.dumps(list_data),
                               headers=headers)

        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json

        if not browsers:
            print("❌ 没有找到浏览器")
            return None

        print(f"找到 {len(browsers)} 个浏览器")

        # 查找已经打开的浏览器
        opened_browser = None
        for browser in browsers:
            if browser.get('status') == 'Active' or browser.get('status') == 'Running':
                opened_browser = browser
                break

        if not opened_browser:
            # 如果没有找到运行中的，使用第一个
            opened_browser = browsers[0]
            print("3. 没有找到运行中的浏览器，尝试连接第一个浏览器")
        else:
            print("3. 找到运行中的浏览器")

        browser_id = opened_browser['id']
        browser_name = opened_browser.get('name', '未知')
        print(f"   浏览器: {browser_name} (ID: {browser_id})")

        # 获取浏览器连接信息（不重新打开）
        print("4. 获取浏览器连接信息...")
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open",
                                    data=json.dumps(open_data),
                                    headers=headers)

        if open_response.status_code != 200:
            print(f"❌ 获取浏览器信息失败: {open_response.text}")
            return None

        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("❌ 获取浏览器信息失败: 响应中没有data字段")
            return None

        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']

        print(f"✅ 获取浏览器信息成功")
        print(f"   驱动路径: {driver}")
        print(f"   调试地址: {http}")

        # 解析调试端口
        debug_port = http.split(':')[-1]
        print(f"   调试端口: {debug_port}")

        # 使用DrissionPage连接
        print("5. 连接到浏览器...")
        try:
            co = ChromiumOptions()
            co.set_browser_path(driver)
            co.set_address(http)

            browser = Chromium(co)
            tab = browser.latest_tab

            # 测试连接
            current_url = tab.url
            current_title = tab.title

            print(f"✅ 连接成功")
            print(f"   当前URL: {current_url}")
            print(f"   当前标题: {current_title}")

            # 返回连接信息
            connection_info = {
                'success': True,
                'browser_id': browser_id,
                'browser_name': browser_name,
                'debug_port': debug_port,
                'debug_address': http,
                'driver_path': driver,
                'browser_object': browser,
                'tab_object': tab
            }

            print("\n" + "=" * 60)
            print("连接信息:")
            print(f"浏览器ID: {browser_id}")
            print(f"浏览器名称: {browser_name}")
            print(f"调试端口: {debug_port}")
            print(f"调试地址: {http}")
            print("=" * 60)

            return connection_info

        except Exception as e:
            print(f"❌ DrissionPage连接失败: {e}")
            return None

    except Exception as e:
        print(f"❌ 连接已打开的比特浏览器失败: {e}")
        return None

def analyze_cf_status(tab):
    """分析CF验证状态"""
    try:
        print("\n" + "=" * 60)
        print("CF验证状态分析")
        print("=" * 60)

        # 基本信息
        print(f"页面标题: {tab.title}")
        print(f"页面URL: {tab.url}")

        # 检查CF验证状态
        title = tab.title.lower()
        if "just a moment" in title:
            print("🛡️ 检测到CF验证页面")
        elif "success" in title:
            print("✅ 可能已通过验证")
        else:
            print("📄 普通页面")

        # 查找CF相关元素
        print("\n查找CF相关元素:")
        try:
            # CF相关div
            cf_divs = tab.eles('x://div[contains(@class, "cf-")]')
            turnstile_divs = tab.eles('x://div[contains(@class, "turnstile")]')
            challenge_divs = tab.eles('x://div[contains(@class, "challenge")]')

            print(f"  CF div: {len(cf_divs)} 个")
            print(f"  Turnstile div: {len(turnstile_divs)} 个")
            print(f"  Challenge div: {len(challenge_divs)} 个")

            # 查找Success元素
            success_elements = tab.eles('x://*[contains(text(), "Success")]')
            print(f"  Success元素: {len(success_elements)} 个")

            # 查找复选框
            checkboxes = tab.eles('x://input[@type="checkbox"]')
            print(f"  复选框: {len(checkboxes)} 个")

            # 查找iframe
            iframes = tab.eles('x://iframe')
            print(f"  iframe: {len(iframes)} 个")

            # 查找单选框（表示正常页面）
            radio_buttons = tab.eles('x://input[@type="radio"]')
            print(f"  单选框: {len(radio_buttons)} 个")

            if len(radio_buttons) > 0:
                print("✅ 发现单选框，页面可能已正常加载")
            elif len(cf_divs) > 0 or len(turnstile_divs) > 0:
                print("🛡️ 发现CF验证元素，页面在验证中")
            else:
                print("⚠️ 页面状态不明确")

        except Exception as e:
            print(f"  查找元素失败: {e}")

        # 使用JavaScript深度分析
        print("\n使用JavaScript深度分析:")
        try:
            js_code = """
            // 查找所有可能的CF相关元素
            function findCFElements() {
                let results = {
                    shadowRoots: 0,
                    cfElements: [],
                    turnstileElements: [],
                    successElements: [],
                    checkboxes: [],
                    iframes: []
                };

                // 查找shadow root
                document.querySelectorAll('*').forEach(elem => {
                    if (elem.shadowRoot) {
                        results.shadowRoots++;
                    }
                });

                // 查找CF相关元素
                document.querySelectorAll('*[class*="cf-"], *[class*="turnstile"], *[class*="challenge"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.cfElements.push({
                        tag: elem.tagName,
                        className: elem.className,
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y}
                    });
                });

                // 查找Success文本
                document.querySelectorAll('*').forEach(elem => {
                    if (elem.textContent && elem.textContent.includes('Success')) {
                        results.successElements.push({
                            tag: elem.tagName,
                            text: elem.textContent.trim().substring(0, 50)
                        });
                    }
                });

                // 查找复选框
                document.querySelectorAll('input[type="checkbox"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.checkboxes.push({
                        id: elem.id,
                        name: elem.name,
                        className: elem.className,
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y}
                    });
                });

                // 查找iframe
                document.querySelectorAll('iframe').forEach(elem => {
                    results.iframes.push({
                        src: elem.src,
                        title: elem.title,
                        id: elem.id
                    });
                });

                return results;
            }

            return findCFElements();
            """

            result = tab.run_js(js_code)

            if result:
                print(f"  Shadow Root数量: {result.get('shadowRoots', 0)}")
                print(f"  CF相关元素: {len(result.get('cfElements', []))}")
                print(f"  Success元素: {len(result.get('successElements', []))}")
                print(f"  可见复选框: {sum(1 for cb in result.get('checkboxes', []) if cb.get('visible'))}")
                print(f"  iframe数量: {len(result.get('iframes', []))}")

                # 显示重要的CF元素
                cf_elements = result.get('cfElements', [])
                if cf_elements:
                    print("\n  重要CF元素:")
                    for i, elem in enumerate(cf_elements[:3]):  # 只显示前3个
                        print(f"    {i+1}. {elem['tag']} - {elem['className'][:30]} - 可见: {elem['visible']}")

                # 显示可点击的复选框
                checkboxes = result.get('checkboxes', [])
                visible_checkboxes = [cb for cb in checkboxes if cb.get('visible')]
                if visible_checkboxes:
                    print("\n  可点击复选框:")
                    for i, cb in enumerate(visible_checkboxes):
                        print(f"    {i+1}. ID: {cb['id']} - 位置: ({cb['position']['x']}, {cb['position']['y']})")

        except Exception as e:
            print(f"  JavaScript分析失败: {e}")

        return True

    except Exception as e:
        print(f"❌ CF状态分析失败: {e}")
        return False

def analyze_page_basic_info(tab):
    """分析页面基本信息"""
    try:
        print("\n" + "=" * 60)
        print("页面基本信息分析")
        print("=" * 60)

        # 基本信息
        print(f"页面标题: {tab.title}")
        print(f"页面URL: {tab.url}")

        # HTML长度
        html_content = tab.html
        html_length = len(html_content)
        print(f"HTML长度: {html_length} 字符")

        # 关键词检查
        html_lower = html_content.lower()
        keywords = ['claim', 'poppi', 'cloudflare', 'turnstile', 'verification']

        print("\n关键词检查:")
        for keyword in keywords:
            found = keyword in html_lower
            status = "✅" if found else "❌"
            print(f"  {status} {keyword}: {found}")

        # 元素统计
        print("\n元素统计:")
        try:
            radio_count = len(tab.eles('x://input[@type="radio"]'))
            button_count = len(tab.eles('x://button'))
            input_count = len(tab.eles('x://input'))
            div_count = len(tab.eles('x://div'))

            print(f"  单选框: {radio_count} 个")
            print(f"  按钮: {button_count} 个")
            print(f"  输入框: {input_count} 个")
            print(f"  DIV元素: {div_count} 个")

        except Exception as e:
            print(f"  元素统计失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 页面分析失败: {e}")
        return False

def main():
    """主函数 - 连接比特浏览器并保持连接"""
    print("DrssionPageMCP连接比特浏览器脚本")
    print("此脚本将保持浏览器连接，供后续操作使用")
    
    # 连接比特浏览器
    connection_info = connect_bit_browser_for_mcp()
    
    if not connection_info or not connection_info['success']:
        print("❌ 连接失败，退出程序")
        return
    
    # 获取tab对象
    tab = connection_info['tab_object']
    browser = connection_info['browser_object']
    
    # 导航到目标页面
    if navigate_to_target_page(tab):
        # 分析页面基本信息
        analyze_page_basic_info(tab)
    
    # 保持连接，等待进一步指令
    print("\n" + "=" * 60)
    print("浏览器连接已建立，等待进一步指令...")
    print("可以使用以下信息进行DrssionPageMCP连接:")
    print(f"调试端口: {connection_info['debug_port']}")
    print(f"调试地址: {connection_info['debug_address']}")
    print("=" * 60)
    
    try:
        # 保持程序运行，等待用户操作
        print("\n按 Ctrl+C 退出程序...")
        while True:
            time.sleep(10)
            # 定期检查连接状态
            try:
                current_title = tab.title
                print(f"连接正常 - 当前页面: {current_title}")
            except:
                print("⚠️ 连接可能已断开")
                break
                
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序异常: {e}")
    finally:
        # 清理资源
        try:
            print("清理资源...")
            browser.quit()
        except:
            pass
        print("程序结束")

def get_bit_browser_debug_port():
    """获取比特浏览器的调试端口，供DrissionPageMCP使用"""
    try:
        print("获取比特浏览器调试端口...")

        # 检查比特浏览器服务
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)

        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list",
                               data=json.dumps(list_data),
                               headers=headers)

        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json

        if not browsers:
            print("❌ 没有找到浏览器")
            return None

        # 查找已经打开的浏览器
        opened_browser = None
        for browser in browsers:
            if browser.get('status') == 'Active' or browser.get('status') == 'Running':
                opened_browser = browser
                break

        if not opened_browser:
            opened_browser = browsers[0]

        browser_id = opened_browser['id']

        # 获取浏览器连接信息
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open",
                                    data=json.dumps(open_data),
                                    headers=headers)

        if open_response.status_code != 200:
            print(f"❌ 获取浏览器信息失败")
            return None

        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            return None

        http = open_response_json['data']['http']
        debug_port = http.split(':')[-1]

        print(f"✅ 获取调试端口成功: {debug_port}")
        return debug_port

    except Exception as e:
        print(f"❌ 获取调试端口失败: {e}")
        return None

def connect_and_analyze():
    """连接已打开的比特浏览器并分析CF状态"""
    print("连接已打开的比特浏览器并分析CF状态")
    print("=" * 60)

    # 连接已打开的比特浏览器
    connection_info = connect_existing_bit_browser()

    if not connection_info or not connection_info['success']:
        print("❌ 连接失败")
        return None

    # 获取tab对象
    tab = connection_info['tab_object']

    # 分析CF状态
    analyze_cf_status(tab)

    print("\n" + "=" * 60)
    print("分析完成！浏览器保持连接状态")
    print("可以使用DrissionPageMCP连接调试端口: " + connection_info['debug_port'])
    print("=" * 60)

    return connection_info

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "connect":
        # 如果参数是connect，只连接不重新打开
        connect_and_analyze()
    else:
        # 默认行为：重新打开浏览器
        main()
