"""
邮箱验证码管理器
根据邮件对接API.md文档实现邮箱验证码的获取功能
"""

import requests
import time
import json
import logging
from typing import Optional, Dict, Any
from .config import config

# 使用标准logging而不是loguru
logger = logging.getLogger(__name__)


class EmailCodeManager:
    """
    邮箱验证码管理器
    用于从邮件API服务获取验证码
    """
    
    def __init__(self, api_base_url: str = None):
        """
        初始化邮箱验证码管理器

        Args:
            api_base_url: API基础地址（可选，默认从配置文件读取）
        """
        # 从配置文件获取API地址，如果没有提供的话
        if api_base_url is None:
            email_config = config.get_email_api_config()
            api_base_url = email_config["api_url"]

        self.api_base_url = api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'EmailCodeManager/1.0'
        })
        
        logger.info(f"📧 邮箱验证码管理器初始化完成，API地址: {api_base_url}")
    
    def check_system_status(self) -> bool:
        """
        检测邮件API系统状态
        
        Returns:
            bool: 系统是否正常运行
        """
        try:
            logger.info(f"🔍 检测邮件API系统状态，访问地址: {self.api_base_url}/api/test")
            response = self.session.get(f"{self.api_base_url}/api/test", timeout=10)
            
            logger.info(f"📡 API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 系统状态检测成功: {result}")
                # 兼容两种响应格式
                return result.get('success', False) or result.get('status') == 'ok'
            else:
                logger.error(f"❌ 系统状态检测失败，HTTP状态码: {response.status_code}")
                logger.error(f"📄 响应内容: {response.text[:200]}...")
                return False
                
        except requests.exceptions.Timeout:
            logger.error(f"❌ 系统状态检测超时（10秒），API地址: {self.api_base_url}")
            logger.error("💡 建议检查网络连接或更换API服务器")
            return False
        except requests.exceptions.ConnectionError as e:
            logger.error(f"❌ 无法连接到邮件API服务: {self.api_base_url}")
            logger.error(f"🔗 连接错误详情: {e}")
            logger.error("💡 建议检查:")
            logger.error("   1. 网络连接是否正常")
            logger.error("   2. API服务器是否可用")
            logger.error("   3. 防火墙是否阻止了连接")
            return False
        except Exception as e:
            logger.error(f"❌ 系统状态检测异常: {e}")
            logger.error(f"🌐 当前API地址: {self.api_base_url}")
            return False
    
    def get_verification_code(self, email: str) -> Optional[str]:
        """
        获取指定邮箱的验证码（单次获取）
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[str]: 验证码，如果获取失败返回None
        """
        try:
            logger.info(f"📧 获取邮箱 {email} 的验证码...")
            
            # 构造请求数据
            request_data = {"email": email}
            
            # 发送POST请求
            response = self.session.post(
                f"{self.api_base_url}/api/email/get",
                json=request_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"📄 API响应内容: {result}")

                if result.get('success'):
                    # 兼容两种API响应格式
                    verification_code = None

                    # 格式1: 直接在根级别 (1.84版本使用的格式)
                    if 'code' in result:
                        verification_code = result.get('code')
                        logger.info(f"✅ 使用格式1获取验证码: {verification_code}")

                    # 格式2: 在data对象中 (API文档描述的格式)
                    elif 'data' in result:
                        data = result.get('data', {})
                        verification_code = data.get('verification_code')
                        if verification_code:
                            logger.info(f"✅ 使用格式2获取验证码: {verification_code}")
                            # 记录详细信息
                            sender_email = data.get('sender_email', '')
                            subject = data.get('subject', '')
                            received_at = data.get('received_at', '')
                            logger.debug(f"📄 邮件详情: 发件人={sender_email}, 主题={subject}, 接收时间={received_at}")

                    if verification_code:
                        return verification_code
                    else:
                        logger.warning("⚠️ API返回成功但验证码字段为空")
                        return None
                else:
                    error_message = result.get('message', '未知错误')
                    error_code = result.get('error_code', 'UNKNOWN')
                    logger.warning(f"⚠️ 获取验证码失败: {error_message} (错误码: {error_code})")
                    return None
                    
            else:
                logger.error(f"❌ 获取验证码失败，HTTP状态码: {response.status_code}")
                logger.error(f"📄 响应内容: {response.text}")

                if response.status_code == 404:
                    logger.info("💡 可能原因: 邮件未到达或已过期 (EMAIL_NOT_FOUND)")
                elif response.status_code == 410:
                    logger.info("💡 可能原因: 邮件已过期或已被读取 (EMAIL_EXPIRED/EMAIL_ALREADY_READ)")
                elif response.status_code == 400:
                    logger.info("💡 可能原因: 邮箱地址格式无效或缺少参数 (INVALID_EMAIL/MISSING_PARAMETER)")
                elif response.status_code == 500:
                    logger.info("💡 可能原因: 系统内部错误 (SYSTEM_ERROR)")

                return None
                
        except requests.exceptions.Timeout:
            logger.error("❌ 获取验证码请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.error("❌ 无法连接到邮件API服务")
            return None
        except Exception as e:
            logger.error(f"❌ 获取验证码异常: {e}")
            return None
    
    def get_verification_code_with_retry(self, email: str, max_retries: int = 3, retry_delay: int = 2) -> Optional[str]:
        """
        带重试机制的获取验证码
        
        Args:
            email: 邮箱地址
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            
        Returns:
            Optional[str]: 验证码，如果获取失败返回None
        """
        logger.info(f"🔄 开始获取验证码，最大重试次数: {max_retries}")
        
        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 第{attempt + 1}次尝试获取验证码...")
                
                code = self.get_verification_code(email)
                if code:
                    logger.info(f"🎉 第{attempt + 1}次尝试成功获取验证码: {code}")
                    return code
                else:
                    logger.info(f"📭 第{attempt + 1}次尝试暂未收到验证码")
                    
                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    logger.info(f"⏳ 等待{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                    
            except Exception as e:
                logger.warning(f"⚠️ 第{attempt + 1}次尝试获取验证码异常: {e}，继续重试...")
                if attempt < max_retries - 1:
                    logger.info(f"⏳ 等待{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
        
        logger.error(f"❌ 经过{max_retries}次尝试仍未获取到验证码")
        return None
    
    def wait_for_verification_code(self, email: str, max_wait_time: int = 40, polling_interval: int = 5) -> Optional[str]:
        """
        轮询等待验证码（8次×5秒检查，共40秒）
        
        Args:
            email: 邮箱地址
            max_wait_time: 最大等待时间（秒），默认40秒（8次×5秒检查）
            polling_interval: 轮询间隔（秒），默认5秒
            
        Returns:
            Optional[str]: 验证码，如果等待超时返回None
        """
        logger.info(f"⏳ 开始等待邮箱 {email} 的验证码，最大等待时间: {max_wait_time}秒，每{polling_interval}秒检查一次")
        
        start_time = time.time()
        attempt_count = 0
        
        while time.time() - start_time < max_wait_time:
            attempt_count += 1
            elapsed_time = int(time.time() - start_time)
            
            logger.info(f"🔍 第{attempt_count}次检查验证码 (已等待{elapsed_time}秒/{max_wait_time}秒)")
            
            try:
                code = self.get_verification_code(email)
                if code:
                    logger.info(f"🎉 成功获取到验证码: {code} (等待了{elapsed_time}秒)")
                    return code
                else:
                    logger.info(f"📭 第{attempt_count}次检查暂未收到验证码，继续等待...")
                    
            except Exception as e:
                logger.warning(f"⚠️ 第{attempt_count}次检查验证码时发生异常: {e}，但继续轮询...")
            
            # 等待指定间隔后继续轮询
            logger.info(f"💤 等待{polling_interval}秒后继续第{attempt_count + 1}次检查...")
            time.sleep(polling_interval)
        
        logger.error(f"❌ 等待验证码超时，已等待{max_wait_time}秒")
        return None
    
    def clear_old_emails(self, email: str) -> bool:
        """
        清除旧邮件，确保获取最新验证码
        通过调用API来触发"阅后即焚"机制，清除可能存在的旧验证码

        Args:
            email: 邮箱地址

        Returns:
            bool: 是否成功清除
        """
        try:
            logger.info(f"🧹 清除邮箱 {email} 的旧验证码...")

            # 尝试获取并丢弃任何现有的验证码
            old_code = self.get_verification_code(email)
            if old_code:
                logger.info(f"🗑️ 发现并清除旧验证码: {old_code}")
                return True
            else:
                logger.info("✅ 没有发现旧验证码，邮箱已清洁")
                return True

        except Exception as e:
            logger.warning(f"⚠️ 清除旧邮件时异常: {e}，但继续流程")
            return False

    def get_verification_code_smart(self, email: str, max_wait_time: int = 40, max_retries: int = 1, clear_old: bool = True) -> Optional[str]:
        """
        智能获取验证码（确保获取最新验证码）

        Args:
            email: 邮箱地址
            max_wait_time: 最大等待时间（秒），默认40秒（8次×5秒检查）
            max_retries: 失败后的重试次数
            clear_old: 是否清除旧验证码，默认True

        Returns:
            Optional[str]: 验证码，如果获取失败返回None
        """
        logger.info(f"🧠 智能获取验证码模式启动，邮箱: {email}")
        logger.info(f"⏰ 将在{max_wait_time}秒内每5秒尝试一次（约{max_wait_time//5}次检查），确保获取最新验证码")

        # 检查系统状态（仅作为信息参考，不影响后续流程）
        try:
            if self.check_system_status():
                logger.info("✅ 邮件API系统状态正常")
            else:
                logger.warning("⚠️ 邮件API系统状态异常，但继续尝试获取验证码")
        except Exception as e:
            logger.warning(f"⚠️ 系统状态检查异常: {e}，但继续尝试获取验证码")

        # 步骤1：清除旧验证码（确保获取最新的）
        if clear_old:
            logger.info("🧹 步骤1：清除可能存在的旧验证码...")
            self.clear_old_emails(email)
            logger.info("✅ 旧验证码清除完成，现在等待新验证码...")

            # 等待一小段时间，让新邮件有时间到达
            logger.info("⏳ 等待3秒，让新验证码邮件有时间到达...")
            time.sleep(3)
        else:
            logger.info("⚠️ 跳过清除旧验证码步骤")

        # 步骤2：开始轮询等待新验证码
        logger.info("🔍 步骤2：开始轮询等待新验证码...")
        code = self.wait_for_verification_code(email, max_wait_time, polling_interval=5)
        if code:
            logger.info(f"✅ 成功获取到最新验证码: {code}")
            return code

        # 步骤3：最后尝试重试机制
        if max_retries > 0:
            logger.info(f"🔄 步骤3：轮询等待完成未获取到验证码，尝试最后的重试机制，最大重试次数: {max_retries}")
            try:
                code = self.get_verification_code_with_retry(email, max_retries, retry_delay=2)
                if code:
                    logger.info(f"✅ 重试机制成功获取验证码: {code}")
                    return code
            except Exception as e:
                logger.warning(f"⚠️ 重试机制异常: {e}")

        logger.error(f"❌ 已在{max_wait_time}秒内坚持尝试，仍未获取到最新验证码")
        logger.error("💡 可能原因:")
        logger.error("   1. 邮件服务器延迟超过预期")
        logger.error("   2. 邮件被垃圾邮件过滤器拦截")
        logger.error("   3. 邮箱地址不正确")
        logger.error("   4. 网站没有发送验证码邮件")
        return None

    def get_verification_code_for_new_email(self, email: str, max_wait_time: int = 40) -> Optional[str]:
        """
        专门用于新邮件输入后获取验证码的函数
        直接获取刚收到的验证码，不执行清除操作

        Args:
            email: 邮箱地址
            max_wait_time: 最大等待时间（秒）

        Returns:
            Optional[str]: 刚收到的验证码
        """
        logger.info(f"📧 为新邮件输入获取验证码: {email}")
        logger.info("🎯 直接获取验证码，不执行清除操作（避免删除刚收到的验证码）")

        # 直接获取验证码，不清除（因为我们要的就是刚刚收到的验证码）
        return self.get_verification_code_smart(email, max_wait_time, clear_old=False)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接并返回详细信息
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        result = {
            'system_online': False,
            'api_base_url': self.api_base_url,
            'test_time': time.time(),
            'response_time': None,
            'error': None
        }
        
        try:
            logger.info("🧪 测试邮件API连接...")
            start_time = time.time()
            
            is_online = self.check_system_status()
            response_time = time.time() - start_time
            
            result['system_online'] = is_online
            result['response_time'] = round(response_time * 1000, 2)  # 转换为毫秒
            
            if is_online:
                logger.info(f"✅ 连接测试成功，响应时间: {result['response_time']}ms")
            else:
                logger.error("❌ 连接测试失败，系统不在线")
                
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ 连接测试异常: {e}")
        
        return result


# 创建全局实例
email_code_manager = EmailCodeManager()


# 便捷函数
def get_email_verification_code(email: str, max_wait_time: int = 40) -> Optional[str]:
    """
    便捷函数：获取邮箱验证码（确保获取最新验证码）

    Args:
        email: 邮箱地址
        max_wait_time: 最大等待时间（秒），默认40秒（8次×5秒检查）

    Returns:
        Optional[str]: 最新的验证码
    """
    return email_code_manager.get_verification_code_for_new_email(email, max_wait_time)


def test_email_api() -> bool:
    """
    便捷函数：测试邮件API是否可用
    
    Returns:
        bool: API是否可用
    """
    return email_code_manager.check_system_status()


if __name__ == "__main__":
    # 测试代码
    logger.info("🧪 开始测试邮箱验证码管理器...")
    
    # 测试连接
    test_result = email_code_manager.test_connection()
    logger.info(f"连接测试结果: {test_result}")
    
    # 测试获取验证码（使用测试邮箱）
    test_email = "<EMAIL>"
    logger.info(f"测试获取验证码，邮箱: {test_email}")
    code = get_email_verification_code(test_email, max_wait_time=10)
    
    if code:
        logger.info(f"✅ 测试成功，获取到验证码: {code}")
    else:
        logger.info("ℹ️ 测试完成，未获取到验证码（正常情况，因为是测试邮箱）") 