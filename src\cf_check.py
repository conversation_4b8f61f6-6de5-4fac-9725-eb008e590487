import threading

class Cloudflare<PERSON>hecker:
    """Cloudflare盾检测与自动点击类"""
    def __init__(self, logger=None):
        self.logger = logger
    def pass_cf_check(self, tab, attempt_num, div_xpath='x:/html/body/div[2]/div/div[1]/div/div', checkbox_type='@type=checkbox', timeout=3):
        
        thread_id = threading.get_ident()
        try:
            self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：查找div元素: {div_xpath}")
            div_ele = tab.ele(div_xpath, timeout=timeout) 
            if attempt_num >= 1:
                  self.logger.info("点击cf盾框")
                  tab.actions.move_to((208, 289)).click()
                  tab.wait(3)
            return False
        except Exception as e:
            if self.logger:
                self.logger.error(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：cf盾处理异常: {e}")
            return False
    
    
    #修改V2的代码(点击固定坐标:),进行固定的cf模拟人手点击方法
    #修改V3的代码(点击固定坐标:),进行固定的cf模拟人手点击方法
    def pass_cf_check_v2(self, tab, attempt_num, div_xpath='x:/html/body/div[2]/div/div[1]/div/div', checkbox_type='@type=checkbox', timeout=3):
        thread_id = threading.get_ident()
        try:
            self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：查找div元素")
            
            # 多种常见的CF盾div路径
            div_xpaths = [
                'x:/html/body/div[2]/div/div[1]/div/div',  # 原始路径
                'x:/html/body/div[1]/div/div[1]/div/div',  # 第一个div容器
                'x:/html/body/div[3]/div/div[1]/div/div',  # 第三个div容器
                'x://div[contains(@class, "cf-challenge")]',  # CF挑战div
                'x://div[contains(@class, "cloudflare")]',   # Cloudflare相关div
                'x://div[contains(@class, "turnstile")]',    # Turnstile相关div
                'x://div[@id="cf-stage"]',                   # CF舞台区域
                'x://div[@id="challenge-stage"]',            # 挑战舞台区域
                'x://div[contains(@class, "challenge-container")]',  # 挑战容器
                'x://div[contains(@class, "cf-browser-verification")]',  # 浏览器验证
                'x://div[contains(@class, "cf-turnstile-wrapper")]',     # Turnstile包装器
                'x://div[contains(@class, "challenge-form")]',          # 挑战表单
                'x://div[contains(@id, "cf-")]',             # 任何包含cf-的id
                'x://div[contains(@data-ray, "")]',          # Ray ID相关
                'x://div[@class="main-wrapper"]',            # 主包装器
                'x://div[@class="challenge-wrapper"]',       # 挑战包装器
                'x://form[@id="challenge-form"]',            # 挑战表单
                'x://div[contains(@class, "spacer")]//parent::div',  # 间隔器的父div
                'x://iframe[@title="Widget containing checkbox for hCaptcha security challenge"]//parent::div',  # hCaptcha父div
                'x://iframe[contains(@src, "cloudflare")]//parent::div'  # Cloudflare iframe父div
            ]
            
            div_ele = None
            used_xpath = None
            
            # 遍历尝试所有div路径
            for xpath in div_xpaths:
                try:
                    div_ele = tab.ele(xpath, timeout=0.5)  # 短超时快速尝试
                    if div_ele:
                        used_xpath = xpath
                        self.logger.info(f"线程 {thread_id} - 找到div元素: {xpath}")
                        break
                except:
                    continue
            
            if not div_ele:
                self.logger.info(f"线程 {thread_id} - 未找到任何div元素，尝试直接坐标点击")
            
            # 如果是第2次及以后的尝试，直接点击坐标
            if attempt_num >= 1:
                self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试，点击cf盾框坐标 (208, 289)")
                tab.actions.move_to((208, 289)).click()
                tab.wait(3)
                
                # 如果找到了div元素，也可以尝试点击div中心
                if div_ele:
                    try:
                        self.logger.info(f"线程 {thread_id} - 同时尝试点击找到的div元素")
                        tab.actions.move_to(div_ele).click()
                        tab.wait(2)
                    except Exception as div_click_e:
                        self.logger.debug(f"线程 {thread_id} - div点击失败: {div_click_e}")
                        
                # 检查是否成功绕过CF盾 - 等待2秒后检查页面
                tab.wait(2)
                current_page = tab.html.lower()
                
                # 检查是否还有CF盾标识
                cf_indicators = ['cloudflare', 'checking your browser', 'please wait', 'verifying you are human']
                has_cf = any(indicator in current_page for indicator in cf_indicators)
                
                if not has_cf:
                    self.logger.info(f"线程 {thread_id} - ✅ CF盾处理成功，页面已跳转")
                    return True
                else:
                    self.logger.info(f"线程 {thread_id} - ⏳ CF盾仍在处理中")
                    return False
            else:
                # 第一次尝试，只是检测CF盾存在
                self.logger.info(f"线程 {thread_id} - 第一次检测CF盾，暂不处理")
                return False
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：cf盾处理异常: {e}")
            return False
    