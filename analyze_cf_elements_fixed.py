#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析CF元素 - 修复版本
"""

import time
import requests
import json
from DrissionPage import ChromiumOptions, Chromium

def connect_bit_browser():
    """连接比特浏览器"""
    try:
        print("连接比特浏览器...")
        headers = {'Content-Type': 'application/json'}
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("❌ 没有找到浏览器")
            return None, None
        
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        open_response_json = open_response.json()
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接失败: {e}")
        return None, None

def analyze_cf_elements_detailed(tab):
    """详细分析CF元素"""
    print("=" * 60)
    print("详细分析CF元素")
    print("=" * 60)
    
    try:
        # 导航到目标页面
        print("1. 导航到目标页面...")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")
        time.sleep(5)
        
        print(f"当前URL: {tab.url}")
        print(f"当前标题: {tab.title}")
        
        # 分析CF相关div
        print("\n2. 分析CF相关div...")
        cf_selectors = [
            'x://div[contains(@class, "cf-")]',
            'x://div[contains(@class, "turnstile")]',
            'x://div[contains(@class, "challenge")]',
            'x://div[@id="cf-stage"]'
        ]
        
        for selector in cf_selectors:
            try:
                elements = tab.eles(selector)
                if elements:
                    print(f"\n选择器: {selector}")
                    print(f"找到 {len(elements)} 个元素")
                    
                    for i, elem in enumerate(elements):
                        try:
                            print(f"  元素 {i+1}:")
                            print(f"    标签: {elem.tag}")
                            print(f"    ID: {elem.attr('id') or '无'}")
                            print(f"    Class: {elem.attr('class') or '无'}")
                            print(f"    文本: {elem.text.strip()[:50] or '无文本'}")
                            
                            # 获取元素位置和大小
                            try:
                                rect = elem.rect
                                print(f"    位置: x={rect.x}, y={rect.y}")
                                print(f"    大小: width={rect.width}, height={rect.height}")
                            except:
                                print(f"    位置: 无法获取")
                            
                            # 检查样式
                            try:
                                style = elem.attr('style') or ''
                                if style:
                                    print(f"    样式: {style[:50]}")
                            except:
                                pass
                                
                        except Exception as e:
                            print(f"  元素 {i+1}: 获取信息失败 - {e}")
                            
            except Exception as e:
                print(f"选择器 {selector} 失败: {e}")
        
        # 查找iframe
        print("\n3. 查找iframe...")
        try:
            iframes = tab.eles('x://iframe')
            print(f"找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                try:
                    print(f"  iframe {i+1}:")
                    print(f"    src: {iframe.attr('src') or '无'}")
                    print(f"    title: {iframe.attr('title') or '无'}")
                    print(f"    id: {iframe.attr('id') or '无'}")
                    print(f"    class: {iframe.attr('class') or '无'}")
                    
                    # 检查是否是CF相关iframe
                    src = iframe.attr('src') or ''
                    title = iframe.attr('title') or ''
                    if 'cloudflare' in src.lower() or 'turnstile' in src.lower() or 'challenge' in title.lower():
                        print(f"    🛡️ 这是CF相关iframe!")
                        
                        # 尝试获取iframe内容
                        try:
                            rect = iframe.rect
                            print(f"    iframe位置: x={rect.x}, y={rect.y}")
                            print(f"    iframe大小: width={rect.width}, height={rect.height}")
                        except:
                            pass
                            
                except Exception as e:
                    print(f"  iframe {i+1}: 获取信息失败 - {e}")
                    
        except Exception as e:
            print(f"查找iframe失败: {e}")
        
        # 查找Success状态
        print("\n4. 查找Success状态...")
        try:
            success_selectors = [
                'x://*[contains(text(), "Success")]',
                'x://*[contains(text(), "✓")]',
                'x://*[contains(text(), "Verified")]',
                'x://*[contains(text(), "Complete")]'
            ]
            
            for selector in success_selectors:
                try:
                    elements = tab.eles(selector)
                    if elements:
                        print(f"选择器 {selector}: 找到 {len(elements)} 个元素")
                        for i, elem in enumerate(elements):
                            try:
                                print(f"  Success元素 {i+1}: {elem.text.strip()}")
                            except:
                                pass
                except:
                    continue
                    
        except Exception as e:
            print(f"查找Success状态失败: {e}")
        
        # 查找可点击的验证元素
        print("\n5. 查找可点击的验证元素...")
        try:
            # 查找复选框
            checkboxes = tab.eles('x://input[@type="checkbox"]')
            print(f"复选框数量: {len(checkboxes)}")
            
            for i, checkbox in enumerate(checkboxes):
                try:
                    print(f"  复选框 {i+1}:")
                    print(f"    name: {checkbox.attr('name') or '无'}")
                    print(f"    id: {checkbox.attr('id') or '无'}")
                    print(f"    class: {checkbox.attr('class') or '无'}")
                    
                    # 检查位置
                    try:
                        rect = checkbox.rect
                        print(f"    位置: x={rect.x}, y={rect.y}")
                        if rect.x > 0 and rect.y > 0:
                            print(f"    🎯 这个复选框可能可以点击!")
                    except:
                        pass
                        
                except Exception as e:
                    print(f"  复选框 {i+1}: 获取信息失败 - {e}")
            
            # 查找可点击的div
            clickable_divs = tab.eles('x://div[@role="button"]')
            print(f"\n可点击div数量: {len(clickable_divs)}")
            
            for i, div in enumerate(clickable_divs):
                try:
                    print(f"  可点击div {i+1}:")
                    print(f"    class: {div.attr('class') or '无'}")
                    print(f"    文本: {div.text.strip()[:30] or '无文本'}")
                    
                    # 检查位置
                    try:
                        rect = div.rect
                        print(f"    位置: x={rect.x}, y={rect.y}")
                        if rect.x > 0 and rect.y > 0:
                            print(f"    🎯 这个div可能可以点击!")
                    except:
                        pass
                        
                except Exception as e:
                    print(f"  可点击div {i+1}: 获取信息失败 - {e}")
                    
        except Exception as e:
            print(f"查找可点击元素失败: {e}")
        
        # 总结
        print("\n" + "=" * 60)
        print("CF状态总结")
        print("=" * 60)
        
        # 检查页面状态
        title = tab.title
        if "just a moment" in title.lower():
            print("🛡️ 页面正在CF验证中")
            print("建议操作:")
            print("1. 等待自动验证完成")
            print("2. 查找并点击验证框")
            print("3. 监控Success状态")
        else:
            print("✅ 页面可能已通过CF验证")
        
        return True
        
    except Exception as e:
        print(f"分析CF元素失败: {e}")
        return False

def monitor_page_changes(tab, duration=30):
    """监控页面变化"""
    print("=" * 60)
    print(f"监控页面变化 ({duration}秒)")
    print("=" * 60)
    
    try:
        start_time = time.time()
        last_title = ""
        
        while time.time() - start_time < duration:
            current_time = round(time.time() - start_time, 1)
            
            try:
                current_title = tab.title
                current_url = tab.url
                
                # 检查标题变化
                if current_title != last_title:
                    print(f"时间 {current_time}s: 标题变化")
                    print(f"  新标题: {current_title}")
                    last_title = current_title
                
                # 检查关键元素
                cf_divs = len(tab.eles('x://div[contains(@class, "cf-")]'))
                turnstile_divs = len(tab.eles('x://div[contains(@class, "turnstile")]'))
                success_elements = len(tab.eles('x://*[contains(text(), "Success")]'))
                radio_buttons = len(tab.eles('x://input[@type="radio"]'))
                
                print(f"时间 {current_time}s: CF div={cf_divs}, Turnstile div={turnstile_divs}, Success={success_elements}, Radio={radio_buttons}")
                
                # 如果发现Success或单选框，说明验证完成
                if success_elements > 0 or radio_buttons > 0:
                    print(f"🎉 时间 {current_time}s: 检测到验证完成!")
                    break
                
            except Exception as e:
                print(f"时间 {current_time}s: 监控失败 - {e}")
            
            time.sleep(2)
    
    except Exception as e:
        print(f"监控页面变化失败: {e}")

def main():
    """主函数"""
    try:
        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print("❌ 连接比特浏览器失败")
            return
        
        print("✅ 成功连接比特浏览器")
        
        # 详细分析CF元素
        analyze_cf_elements_detailed(tab)
        
        # 监控页面变化
        monitor_page_changes(tab, 30)
        
        # 保持浏览器打开
        print(f"\n保持浏览器打开60秒以便观察...")
        time.sleep(60)
        
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

if __name__ == "__main__":
    main()
