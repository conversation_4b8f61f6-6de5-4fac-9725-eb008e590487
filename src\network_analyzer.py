"""
网络数据分析模块
处理和保存抓包数据，提供分析功能
"""

import json
import time
import os
import logging
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class NetworkDataAnalyzer:
    """网络数据分析器"""
    
    def __init__(self, data_dir: str = "network_data"):
        """
        初始化网络数据分析器
        
        Args:
            data_dir: 数据保存目录
        """
        self.data_dir = data_dir
        self.lock = threading.Lock()
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 文件路径
        self.raw_data_file = os.path.join(data_dir, "raw_requests.jsonl")
        self.analysis_file = os.path.join(data_dir, "analysis_results.json")
        self.summary_file = os.path.join(data_dir, "session_summary.txt")
        
    def save_raw_requests(self, requests_data: List[Dict[str, Any]], session_id: str = None) -> str:
        """
        保存原始请求数据
        
        Args:
            requests_data: 请求数据列表
            session_id: 会话ID
            
        Returns:
            str: 保存的会话ID
        """
        if session_id is None:
            session_id = f"session_{int(time.time())}"
        
        with self.lock:
            try:
                timestamp = datetime.now().isoformat()
                
                # 保存到JSONL文件（每行一个JSON对象）
                with open(self.raw_data_file, 'a', encoding='utf-8') as f:
                    session_data = {
                        "session_id": session_id,
                        "timestamp": timestamp,
                        "request_count": len(requests_data),
                        "requests": requests_data
                    }
                    f.write(json.dumps(session_data, ensure_ascii=False) + "\n")
                
                logger.info(f"保存原始请求数据: {len(requests_data)} 个请求，会话ID: {session_id}")
                
                # 更新会话摘要
                self._update_session_summary(session_id, len(requests_data), timestamp)
                
                return session_id
                
            except Exception as e:
                logger.error(f"保存原始请求数据失败: {e}")
                return session_id
    
    def analyze_requests(self, requests_data: List[Dict[str, Any]], session_id: str) -> Dict[str, Any]:
        """
        分析请求数据
        
        Args:
            requests_data: 请求数据列表
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            analysis_result = {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "total_requests": len(requests_data),
                "post_requests": [],
                "get_requests": [],
                "api_endpoints": [],
                "form_submissions": [],
                "csrf_tokens": [],
                "turnstile_data": [],
                "cookies": [],
                "headers_analysis": {},
                "protocol_feasibility": {
                    "feasible": False,
                    "confidence": 0,
                    "reasons": []
                }
            }
            
            # 分析每个请求
            for request in requests_data:
                self._analyze_single_request(request, analysis_result)
            
            # 分析协议可行性
            self._analyze_protocol_feasibility(analysis_result)
            
            # 保存分析结果
            self._save_analysis_result(analysis_result)
            
            logger.info(f"请求分析完成: {session_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析请求数据失败: {e}")
            return {}
    
    def _analyze_single_request(self, request: Dict[str, Any], analysis_result: Dict[str, Any]):
        """
        分析单个请求
        
        Args:
            request: 请求数据
            analysis_result: 分析结果对象
        """
        try:
            method = request.get("method", "").upper()
            url = request.get("url", "")
            headers = request.get("headers", {})
            data = request.get("data", {})
            
            # 按方法分类
            if method == "POST":
                analysis_result["post_requests"].append({
                    "url": url,
                    "headers": headers,
                    "data": data,
                    "content_type": headers.get("content-type", "")
                })
                
                # 检查是否为表单提交
                if "form" in headers.get("content-type", "").lower():
                    analysis_result["form_submissions"].append({
                        "url": url,
                        "form_data": data
                    })
                
                # 检查API端点
                if any(keyword in url.lower() for keyword in ["api", "ajax", "submit", "process"]):
                    analysis_result["api_endpoints"].append(url)
                    
            elif method == "GET":
                analysis_result["get_requests"].append({
                    "url": url,
                    "headers": headers
                })
            
            # 查找CSRF token
            self._extract_csrf_tokens(headers, data, analysis_result)
            
            # 查找Turnstile相关数据
            self._extract_turnstile_data(headers, data, url, analysis_result)
            
            # 收集cookies
            if "cookie" in headers:
                analysis_result["cookies"].append(headers["cookie"])
            
            # 分析请求头
            self._analyze_headers(headers, analysis_result)
            
        except Exception as e:
            logger.error(f"分析单个请求失败: {e}")
    
    def _extract_csrf_tokens(self, headers: Dict[str, Any], data: Dict[str, Any], analysis_result: Dict[str, Any]):
        """提取CSRF token"""
        try:
            # 从headers中查找
            for key, value in headers.items():
                if "csrf" in key.lower() or "token" in key.lower():
                    analysis_result["csrf_tokens"].append({
                        "source": "header",
                        "key": key,
                        "value": value
                    })
            
            # 从data中查找
            if isinstance(data, dict):
                for key, value in data.items():
                    if "csrf" in key.lower() or "_token" in key.lower():
                        analysis_result["csrf_tokens"].append({
                            "source": "form_data",
                            "key": key,
                            "value": value
                        })
                        
        except Exception as e:
            logger.error(f"提取CSRF token失败: {e}")
    
    def _extract_turnstile_data(self, headers: Dict[str, Any], data: Dict[str, Any], url: str, analysis_result: Dict[str, Any]):
        """提取Turnstile相关数据"""
        try:
            # 从URL中查找
            if "turnstile" in url.lower() or "cloudflare" in url.lower():
                analysis_result["turnstile_data"].append({
                    "type": "url",
                    "url": url,
                    "headers": headers
                })
            
            # 从表单数据中查找
            if isinstance(data, dict):
                for key, value in data.items():
                    if "turnstile" in key.lower() or "cf-" in key.lower():
                        analysis_result["turnstile_data"].append({
                            "type": "form_field",
                            "key": key,
                            "value": value
                        })
                        
        except Exception as e:
            logger.error(f"提取Turnstile数据失败: {e}")
    
    def _analyze_headers(self, headers: Dict[str, Any], analysis_result: Dict[str, Any]):
        """分析请求头"""
        try:
            headers_analysis = analysis_result["headers_analysis"]
            
            # 统计常见头部
            common_headers = ["user-agent", "referer", "origin", "accept", "content-type"]
            for header in common_headers:
                if header in headers:
                    if header not in headers_analysis:
                        headers_analysis[header] = []
                    headers_analysis[header].append(headers[header])
                    
        except Exception as e:
            logger.error(f"分析请求头失败: {e}")
    
    def _analyze_protocol_feasibility(self, analysis_result: Dict[str, Any]):
        """分析协议可行性"""
        try:
            feasibility = analysis_result["protocol_feasibility"]
            confidence = 0
            reasons = []
            
            # 检查POST请求
            post_count = len(analysis_result["post_requests"])
            if post_count > 0:
                confidence += 30
                reasons.append(f"发现 {post_count} 个POST请求")
            
            # 检查API端点
            api_count = len(analysis_result["api_endpoints"])
            if api_count > 0:
                confidence += 40
                reasons.append(f"发现 {api_count} 个API端点")
            
            # 检查表单提交
            form_count = len(analysis_result["form_submissions"])
            if form_count > 0:
                confidence += 20
                reasons.append(f"发现 {form_count} 个表单提交")
            
            # 检查CSRF token
            csrf_count = len(analysis_result["csrf_tokens"])
            if csrf_count > 0:
                confidence += 10
                reasons.append(f"发现 {csrf_count} 个CSRF token")
            else:
                reasons.append("未发现CSRF token，可能更容易实现协议提交")
            
            # 检查Turnstile数据
            turnstile_count = len(analysis_result["turnstile_data"])
            if turnstile_count > 0:
                confidence -= 20  # Turnstile增加复杂性
                reasons.append(f"发现 {turnstile_count} 个Turnstile验证，需要处理")
            
            # 设置可行性
            feasibility["confidence"] = max(0, min(100, confidence))
            feasibility["feasible"] = confidence >= 50
            feasibility["reasons"] = reasons
            
            if feasibility["feasible"]:
                reasons.append("建议：可以尝试协议方式提交")
            else:
                reasons.append("建议：继续使用浏览器自动化方式")
                
        except Exception as e:
            logger.error(f"分析协议可行性失败: {e}")
    
    def _save_analysis_result(self, analysis_result: Dict[str, Any]):
        """保存分析结果"""
        try:
            # 读取现有分析结果
            existing_results = []
            if os.path.exists(self.analysis_file):
                with open(self.analysis_file, 'r', encoding='utf-8') as f:
                    existing_results = json.load(f)
            
            # 添加新结果
            existing_results.append(analysis_result)
            
            # 保存更新后的结果
            with open(self.analysis_file, 'w', encoding='utf-8') as f:
                json.dump(existing_results, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
    
    def _update_session_summary(self, session_id: str, request_count: int, timestamp: str):
        """更新会话摘要"""
        try:
            with open(self.summary_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] 会话: {session_id}, 请求数: {request_count}\n")
                
        except Exception as e:
            logger.error(f"更新会话摘要失败: {e}")
    
    def get_latest_analysis(self) -> Optional[Dict[str, Any]]:
        """获取最新的分析结果"""
        try:
            if os.path.exists(self.analysis_file):
                with open(self.analysis_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    return results[-1] if results else None
            return None
            
        except Exception as e:
            logger.error(f"获取最新分析结果失败: {e}")
            return None
    
    def get_all_sessions(self) -> List[str]:
        """获取所有会话ID"""
        try:
            sessions = []
            if os.path.exists(self.raw_data_file):
                with open(self.raw_data_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            data = json.loads(line)
                            sessions.append(data.get("session_id"))
            return sessions
            
        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return []
    
    def compare_sessions(self, session_id1: str, session_id2: str) -> Dict[str, Any]:
        """比较两个会话的差异"""
        try:
            session1_data = self._get_session_data(session_id1)
            session2_data = self._get_session_data(session_id2)
            
            if not session1_data or not session2_data:
                return {"error": "无法找到指定的会话数据"}
            
            comparison = {
                "session1": session_id1,
                "session2": session_id2,
                "request_count_diff": len(session2_data["requests"]) - len(session1_data["requests"]),
                "new_endpoints": [],
                "changed_parameters": [],
                "timestamp_diff": session2_data["timestamp"]
            }
            
            # 比较端点
            session1_urls = {req.get("url") for req in session1_data["requests"]}
            session2_urls = {req.get("url") for req in session2_data["requests"]}
            comparison["new_endpoints"] = list(session2_urls - session1_urls)
            
            return comparison
            
        except Exception as e:
            logger.error(f"比较会话失败: {e}")
            return {"error": str(e)}
    
    def _get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取指定会话的数据"""
        try:
            if os.path.exists(self.raw_data_file):
                with open(self.raw_data_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            data = json.loads(line)
                            if data.get("session_id") == session_id:
                                return data
            return None
            
        except Exception as e:
            logger.error(f"获取会话数据失败: {e}")
            return None
    
    def generate_protocol_template(self, session_id: str = None) -> str:
        """
        基于分析结果生成协议模板代码
        
        Args:
            session_id: 会话ID，如果为None则使用最新的分析结果
            
        Returns:
            str: 生成的协议模板代码
        """
        try:
            if session_id:
                analysis = self._get_analysis_by_session(session_id)
            else:
                analysis = self.get_latest_analysis()
            
            if not analysis:
                return "# 未找到分析数据，无法生成模板"
            
            template = self._build_protocol_template(analysis)
            
            # 保存模板到文件
            template_file = os.path.join(self.data_dir, f"protocol_template_{session_id or 'latest'}.py")
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template)
            
            logger.info(f"协议模板已生成: {template_file}")
            return template
            
        except Exception as e:
            logger.error(f"生成协议模板失败: {e}")
            return f"# 生成模板失败: {e}"
    
    def _get_analysis_by_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """根据会话ID获取分析结果"""
        try:
            if os.path.exists(self.analysis_file):
                with open(self.analysis_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    for result in results:
                        if result.get("session_id") == session_id:
                            return result
            return None
            
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return None
    
    def _build_protocol_template(self, analysis: Dict[str, Any]) -> str:
        """构建协议模板代码"""
        session_id = analysis.get("session_id", "unknown")
        post_requests = analysis.get("post_requests", [])
        csrf_tokens = analysis.get("csrf_tokens", [])
        turnstile_data = analysis.get("turnstile_data", [])
        feasibility = analysis.get("protocol_feasibility", {})
        
        template = f'''"""
基于抓包分析生成的协议请求模板
会话ID: {session_id}
生成时间: {datetime.now().isoformat()}
可行性评估: {"可行" if feasibility.get("feasible") else "不建议"} (置信度: {feasibility.get("confidence", 0)}%)
"""

import requests
import json
import time
from typing import Dict, Any, Optional


class VeritaConnectProtocol:
    """Verita Connect协议请求类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://veritaconnect.com"
        self.headers = {{
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/html, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive"
        }}
        self.session.headers.update(self.headers)
        
        # 存储重要的token和数据
        self.csrf_token = None
        self.turnstile_token = None
        self.session_cookies = {{}}
'''

        # 添加CSRF token处理
        if csrf_tokens:
            template += '''
    def extract_csrf_token(self, html_content: str) -> Optional[str]:
        """提取CSRF token"""
        import re
        
        # 基于抓包分析的token模式
        patterns = [
'''
            for token in csrf_tokens[:3]:  # 只取前3个作为示例
                if token.get("source") == "form_data":
                    template += f'            r\'<input[^>]*name="{token.get("key")}"[^>]*value="([^"]+)"\',\n'
            
            template += '''        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content)
            if match:
                return match.group(1)
        
        return None
'''

        # 添加主要的POST请求方法
        for i, post_req in enumerate(post_requests[:2]):  # 只处理前2个POST请求
            url_path = post_req.get("url", "").replace("https://veritaconnect.com", "")
            method_name = f"submit_request_{i+1}"
            
            template += f'''
    def {method_name}(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交请求到: {url_path}
        """
        try:
            url = f"{{self.base_url}}{url_path}"
            
            # 添加必要的token
            if self.csrf_token:
                form_data["_token"] = self.csrf_token
            
            if self.turnstile_token:
                form_data["cf-turnstile-response"] = self.turnstile_token
            
            response = self.session.post(url, data=form_data)
            
            return {{
                "success": response.status_code in [200, 302],
                "status_code": response.status_code,
                "content": response.text[:500],
                "redirect_url": response.url if response.status_code == 302 else None
            }}
            
        except Exception as e:
            return {{"success": False, "error": str(e)}}
'''

        # 添加使用示例
        template += '''

# 使用示例
if __name__ == "__main__":
    protocol = VeritaConnectProtocol()
    
    print("开始协议测试...")
    
    # 根据实际需要调用相应的方法
    # 示例数据，请根据实际情况修改
    form_data = {
        "field1": "value1",
        "field2": "value2"
        # 添加其他必要字段
    }
    
    # result = protocol.submit_request_1(form_data)
    # print(f"提交结果: {result}")
'''

        return template


# 全局网络分析器实例
network_analyzer = NetworkDataAnalyzer()
