"""
验证码识别模块
使用YesCaptcha服务处理验证码识别
"""

import time
import logging
from typing import Optional, Dict, Any
import requests

logger = logging.getLogger(__name__)


class YesCaptchaSolver:
    """YesCaptcha验证码识别器"""

    def __init__(self, api_key: str, soft_id: str = "23127"):
        """
        初始化YesCaptcha识别器

        Args:
            api_key: YesCaptcha API密钥
            soft_id: 开发者账号ID，用于分层
        """
        self.api_key = api_key
        self.soft_id = soft_id
        self.base_url = "https://api.yescaptcha.com"
        self.session = requests.Session()
        self.session.timeout = 30
    
    def solve_turnstile(self, site_key: str, page_url: str, timeout: int = 300) -> Optional[str]:
        """
        解决Cloudflare Turnstile验证

        Args:
            site_key: 网站密钥
            page_url: 页面URL
            timeout: 超时时间

        Returns:
            Optional[str]: 验证token
        """
        try:
            # 提交Turnstile任务
            task_data = {
                "clientKey": self.api_key,
                "softID": self.soft_id,
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": page_url,
                    "websiteKey": site_key
                }
            }

            response = self.session.post(
                f"{self.base_url}/createTask",
                json=task_data
            )

            if response.status_code != 200:
                logger.error(f"提交Turnstile任务失败: {response.status_code}")
                return None

            result = response.json()
            if result.get("errorId") != 0:
                logger.error(f"Turnstile任务提交错误: {result.get('errorDescription')}")
                return None

            task_id = result.get("taskId")
            if not task_id:
                logger.error("未获取到任务ID")
                return None

            logger.info(f"Turnstile任务已提交，任务ID: {task_id}")

            # 等待结果
            return self._wait_for_result(task_id, timeout)

        except Exception as e:
            logger.error(f"解决Turnstile验证失败: {e}")
            return None
    
    def get_balance(self) -> Optional[float]:
        """
        获取账户余额

        Returns:
            Optional[float]: 账户余额
        """
        try:
            balance_data = {
                "clientKey": self.api_key,
                "softID": self.soft_id
            }

            response = self.session.post(
                f"{self.base_url}/getBalance",
                json=balance_data
            )

            if response.status_code != 200:
                logger.error(f"获取余额失败: {response.status_code}")
                return None

            result = response.json()

            if result.get("errorId") != 0:
                logger.error(f"获取余额错误: {result.get('errorDescription')}")
                return None

            balance = result.get("balance")
            logger.info(f"YesCaptcha账户余额: {balance}")
            return balance

        except Exception as e:
            logger.error(f"获取余额失败: {e}")
            return None
    
    def _wait_for_result(self, task_id: str, timeout: int) -> Optional[str]:
        """
        等待验证码识别结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            Optional[str]: 识别结果
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                result_data = {
                    "clientKey": self.api_key,
                    "softID": self.soft_id,
                    "taskId": task_id
                }
                
                response = self.session.post(
                    f"{self.base_url}/getTaskResult",
                    json=result_data
                )
                
                if response.status_code != 200:
                    logger.error(f"获取任务结果失败: {response.status_code}")
                    time.sleep(5)
                    continue
                
                result = response.json()

                # 添加调试信息
                logger.info(f"获取任务结果: {result}")

                if result.get("errorId") != 0:
                    logger.error(f"获取任务结果错误: {result.get('errorDescription')}")
                    return None

                status = result.get("status")
                
                if status == "ready":
                    solution = result.get("solution", {})

                    # 根据YesCaptcha官网文档，Turnstile的token字段可能是不同的名称
                    token = None
                    if "token" in solution:
                        token = solution["token"]
                    elif "cf_clearance" in solution:
                        token = solution["cf_clearance"]
                    elif "response" in solution:
                        token = solution["response"]

                    if token:
                        logger.info(f"Turnstile验证完成，token: {token[:50]}...")
                        return token
                    else:
                        logger.error(f"未获取到Turnstile token，solution内容: {solution}")
                        return None
                
                elif status == "processing":
                    elapsed = time.time() - start_time
                    logger.info(f"验证码识别中... ({elapsed:.1f}s)")
                    time.sleep(5)
                    continue
                
                else:
                    logger.error(f"任务状态异常: {status}")
                    return None
                
            except Exception as e:
                logger.error(f"等待验证码结果时出错: {e}")
                time.sleep(5)
        
        logger.error("验证码识别超时")
        return None
    

