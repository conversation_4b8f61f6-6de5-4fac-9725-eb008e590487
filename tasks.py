"""
任务管理模块
定义和管理各种自动化任务
"""

import time
import logging
from typing import Dict, Any, Optional, Callable, List
from src.cloudflare_bypass import CloudflareBypass
from src.captcha_solver import YesCaptchaSolver
from src.config import config
from src.file_handler import file_handler
from src.network_analyzer import network_analyzer

logger = logging.getLogger(__name__)


class ClaimTask:
    """索赔任务类"""

    def __init__(self, task_id: str, config: Dict[str, Any]):
        """
        初始化索赔任务

        Args:
            task_id: 任务ID
            config: 任务配置
        """
        self.task_id = task_id
        self.config = config
        self.status = "pending"
        self.result = {}
        self.error_message = ""
        self.start_time = None
        self.end_time = None

        # 初始化组件
        self.cloudflare_bypass = CloudflareBypass()
        self.turnstile_solver = None

        # MCP工具函数（需要外部注入）
        self.mcp_functions = {}

    def set_mcp_functions(self, functions: Dict[str, Callable]):
        """
        设置MCP工具函数

        Args:
            functions: MCP工具函数字典
        """
        self.mcp_functions = functions

    def set_turnstile_solver(self, solver: YesCaptchaSolver):
        """
        设置Turnstile识别器

        Args:
            solver: YesCaptcha识别器实例
        """
        self.turnstile_solver = solver

    def execute(self) -> Dict[str, Any]:
        """
        执行索赔任务

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            self.status = "running"
            self.start_time = time.time()
            logger.info(f"开始执行索赔任务: {self.task_id}")

            # 执行任务步骤
            result = self._execute_claim_steps()

            # 保存网络数据
            if result.get("success") and result.get("steps", {}).get("requests_captured"):
                try:
                    requests_data = result["steps"]["requests_captured"]
                    session_id = f"{self.task_id}_{int(time.time())}"

                    # 保存原始数据
                    network_analyzer.save_raw_requests(requests_data, session_id)

                    # 分析数据
                    analysis_result = network_analyzer.analyze_requests(requests_data, session_id)

                    # 保存到文件处理器
                    file_handler.save_network_data(session_id, requests_data, analysis_result)

                    # 添加分析结果到任务结果
                    result["network_analysis"] = analysis_result
                    result["session_id"] = session_id

                    logger.info(f"网络数据已保存和分析: {session_id}")

                except Exception as e:
                    logger.error(f"保存网络数据失败: {e}")

            if result.get("success"):
                self.status = "completed"
                logger.info(f"索赔任务完成: {self.task_id}")
            else:
                self.status = "failed"
                self.error_message = result.get("error", "未知错误")
                logger.error(f"索赔任务失败: {self.task_id}, 错误: {self.error_message}")

            self.result = result
            self.end_time = time.time()

            return {
                "task_id": self.task_id,
                "status": self.status,
                "result": self.result,
                "error": self.error_message,
                "duration": self.end_time - self.start_time if self.start_time else 0
            }

        except Exception as e:
            self.status = "error"
            self.error_message = str(e)
            self.end_time = time.time()
            logger.error(f"索赔任务执行异常: {self.task_id}, 异常: {e}")

            return {
                "task_id": self.task_id,
                "status": self.status,
                "error": self.error_message,
                "duration": self.end_time - self.start_time if self.start_time else 0
            }