# 索赔自动化系统 v20250730_151416

## 使用说明

### 1. 首次使用配置
1. 将 `config.example.json` 复制为 `config.json`
2. 编辑 `config.json` 文件，填入相关API配置
3. 确保比特浏览器已启动并运行在默认端口(54345)

### 2. 准备数据
1. 编辑 `list01.txt` 文件
2. 按照格式添加待处理的数据，每行一条记录
3. 数据格式：姓名----地址----城市----州----邮编----电话----邮箱等

### 3. 运行程序
1. 双击 `索赔自动化系统.exe` 启动程序
2. 根据菜单提示选择相应操作
3. 选择"运行自动化索赔"开始处理

### 4. 查看结果
- 成功记录会保存在 `listsp_ok.txt` 文件中
- 失败记录会保存在 `listsp_ng.txt` 文件中
- 程序运行日志会保存在 `claim_automation.log` 文件中

### 5. 注意事项
- 确保网络连接正常
- 确保比特浏览器正常运行
- 建议先进行浏览器连接测试
- 程序运行过程中请勿关闭比特浏览器

### 6. 技术支持
如遇问题，请查看日志文件或联系技术支持。

构建时间: 2025-07-30 15:14:16
