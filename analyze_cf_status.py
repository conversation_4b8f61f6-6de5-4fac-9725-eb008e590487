#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析页面上的CF（Cloudflare）状态
"""

import time
import requests
import json
from DrissionPage import ChromiumOptions, Chromium

def connect_bit_browser():
    """连接比特浏览器"""
    try:
        print("连接比特浏览器...")
        headers = {'Content-Type': 'application/json'}
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("❌ 没有找到浏览器")
            return None, None
        
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        open_response_json = open_response.json()
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接失败: {e}")
        return None, None

def analyze_cf_status(tab):
    """分析CF状态"""
    print("=" * 60)
    print("分析页面CF（Cloudflare）状态")
    print("=" * 60)
    
    try:
        # 导航到目标页面
        print("1. 导航到目标页面...")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")
        time.sleep(3)
        
        print(f"当前URL: {tab.url}")
        print(f"当前标题: {tab.title}")
        
        # 分析页面HTML内容
        print("\n2. 分析页面HTML内容...")
        html_content = tab.html
        html_lower = html_content.lower()
        
        # CF相关关键词检查
        cf_keywords = {
            'cloudflare': 'cloudflare' in html_lower,
            'turnstile': 'turnstile' in html_lower,
            'checking your browser': 'checking your browser' in html_lower,
            'please wait': 'please wait' in html_lower,
            'verifying you are human': 'verifying you are human' in html_lower,
            'just a moment': 'just a moment' in html_lower,
            'cf-challenge': 'cf-challenge' in html_lower,
            'cf-browser-verification': 'cf-browser-verification' in html_lower
        }
        
        print("CF关键词检查:")
        cf_detected = False
        for keyword, found in cf_keywords.items():
            status = "✅" if found else "❌"
            print(f"  {status} {keyword}: {found}")
            if found:
                cf_detected = True
        
        # 查找CF相关元素
        print("\n3. 查找CF相关元素...")
        
        # CF挑战相关的div
        cf_div_selectors = [
            'x://div[contains(@class, "cf-challenge")]',
            'x://div[contains(@class, "cloudflare")]',
            'x://div[contains(@class, "turnstile")]',
            'x://div[@id="cf-stage"]',
            'x://div[@id="challenge-stage"]',
            'x://div[contains(@class, "challenge-container")]',
            'x://div[contains(@class, "cf-browser-verification")]',
            'x://div[contains(@class, "cf-turnstile-wrapper")]'
        ]
        
        cf_divs_found = []
        for selector in cf_div_selectors:
            try:
                elements = tab.eles(selector)
                if elements:
                    cf_divs_found.append({
                        'selector': selector,
                        'count': len(elements),
                        'elements': elements
                    })
                    print(f"✅ 找到CF div: {selector} ({len(elements)}个)")
            except:
                continue
        
        if not cf_divs_found:
            print("❌ 未找到CF相关div元素")
        
        # 查找iframe（CF验证通常在iframe中）
        print("\n4. 查找iframe元素...")
        try:
            iframes = tab.eles('x://iframe')
            print(f"找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                try:
                    src = iframe.attr('src') or '无src'
                    title = iframe.attr('title') or '无title'
                    id_attr = iframe.attr('id') or '无id'
                    
                    print(f"  iframe {i+1}:")
                    print(f"    src: {src}")
                    print(f"    title: {title}")
                    print(f"    id: {id_attr}")
                    
                    # 检查是否是CF相关iframe
                    if 'cloudflare' in src.lower() or 'turnstile' in src.lower() or 'challenge' in title.lower():
                        print(f"    🛡️ 这是CF相关iframe!")
                        
                except Exception as e:
                    print(f"  iframe {i+1}: 获取属性失败 - {e}")
                    
        except Exception as e:
            print(f"查找iframe失败: {e}")
        
        # 查找Success状态
        print("\n5. 查找Success状态...")
        try:
            success_elements = tab.eles('x://*[contains(text(), "Success")]')
            if success_elements:
                print(f"✅ 找到 {len(success_elements)} 个Success元素")
                for i, elem in enumerate(success_elements):
                    try:
                        text = elem.text.strip()
                        tag = elem.tag
                        print(f"  Success {i+1}: <{tag}> {text}")
                    except:
                        pass
            else:
                print("❌ 未找到Success状态")
        except Exception as e:
            print(f"查找Success状态失败: {e}")
        
        # 查找验证按钮或复选框
        print("\n6. 查找验证按钮或复选框...")
        try:
            # 查找可能的验证复选框
            checkboxes = tab.eles('x://input[@type="checkbox"]')
            print(f"找到 {len(checkboxes)} 个复选框")
            
            for i, checkbox in enumerate(checkboxes):
                try:
                    name = checkbox.attr('name') or '无name'
                    id_attr = checkbox.attr('id') or '无id'
                    class_attr = checkbox.attr('class') or '无class'
                    
                    print(f"  复选框 {i+1}: name='{name}', id='{id_attr}'")
                    print(f"    class='{class_attr[:50]}'")
                    
                    # 检查是否是CF验证复选框
                    if 'cf-' in id_attr or 'turnstile' in class_attr or 'challenge' in class_attr:
                        print(f"    🛡️ 这可能是CF验证复选框!")
                        
                except Exception as e:
                    print(f"  复选框 {i+1}: 获取属性失败 - {e}")
                    
        except Exception as e:
            print(f"查找复选框失败: {e}")
        
        # 总结CF状态
        print("\n" + "=" * 60)
        print("CF状态总结:")
        print("=" * 60)
        
        if cf_detected:
            print("🛡️ 检测到Cloudflare验证")
            print("建议操作:")
            print("1. 等待自动验证完成")
            print("2. 如有验证框，点击验证")
            print("3. 查找Success状态确认通过")
        else:
            print("✅ 未检测到明显的Cloudflare验证")
            print("页面可能已经通过验证或无需验证")
        
        # 检查页面是否有正常内容
        print("\n7. 检查页面正常内容...")
        try:
            radio_buttons = tab.eles('x://input[@type="radio"]')
            buttons = tab.eles('x://button')
            
            print(f"单选框数量: {len(radio_buttons)}")
            print(f"按钮数量: {len(buttons)}")
            
            if len(radio_buttons) > 0 or len(buttons) > 0:
                print("✅ 页面包含正常的交互元素")
            else:
                print("⚠️ 页面可能仍在CF验证中")
                
        except Exception as e:
            print(f"检查正常内容失败: {e}")
        
        return cf_detected
        
    except Exception as e:
        print(f"分析CF状态失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print("❌ 连接比特浏览器失败")
            return
        
        print("✅ 成功连接比特浏览器")
        
        # 分析CF状态
        cf_detected = analyze_cf_status(tab)
        
        # 保持浏览器打开以便观察
        print(f"\n保持浏览器打开60秒以便观察...")
        print("可以手动检查页面状态...")
        time.sleep(60)
        
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

if __name__ == "__main__":
    main()
