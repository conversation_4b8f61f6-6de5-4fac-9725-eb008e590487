# 📱 API配置说明

## 🎯 **重要修正完成**

基于你的反馈"大傻逼,在验证步骤手机号码又没输入,我1.84是通过api获取的手机号码,你输入文本里的手机号码肯定是错误的,相同道理,获取手机验证码的逻辑难道不是API获取的,又是你生成的?"

**现在已经完全修正，使用API获取真实手机号码和验证码，不再使用文本中的假数据！**

## 🔧 **配置接口说明**

### **1. 手机号码API配置**

在 `config.json` 中配置手机号码API：

```json
{
  "phone_api": {
    "api_url": "https://your-phone-api-domain.com",
    "api_key": "your-phone-api-key-here",
    "service": "payment",
    "country": "us", 
    "platform": "fixdestar",
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 5,
    "verification_timeout": 60,
    "verification_interval": 3
  }
}
```

**配置项说明**：
- `api_url`: 手机号码API服务地址
- `api_key`: API密钥
- `service`: 服务类型（payment）
- `country`: 国家代码（us）
- `platform`: 平台标识（fixdestar）
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数
- `retry_delay`: 重试延迟（秒）
- `verification_timeout`: 验证码获取超时时间（秒）
- `verification_interval`: 验证码轮询间隔（秒）

### **2. 邮箱验证码API配置**

```json
{
  "email_api": {
    "api_url": "https://your-email-api-domain.com",
    "api_key": "your-email-api-key-here",
    "timeout": 30,
    "max_retries": 3
  }
}
```

**配置项说明**：
- `api_url`: 邮箱API服务地址
- `api_key`: API密钥
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数

## 🚀 **配置方法**

### **方法1：通过程序配置**

1. 运行 `python main.py`
2. 选择 `3. 配置设置`
3. 按提示输入API地址和密钥

### **方法2：直接编辑配置文件**

1. 复制 `config.example.json` 为 `config.json`
2. 编辑 `config.json`，填入真实的API地址和密钥
3. 保存文件

### **方法3：环境变量（可选）**

可以通过环境变量设置：
```bash
export PHONE_API_URL="https://your-phone-api-domain.com"
export PHONE_API_KEY="your-phone-api-key-here"
export EMAIL_API_URL="https://your-email-api-domain.com"
export EMAIL_API_KEY="your-email-api-key-here"
```

## 📋 **API接口规范**

### **手机号码获取接口**

**请求**：
```
POST /api/phone
Content-Type: application/json

{
  "api_key": "your-api-key",
  "window_id": "window_12345_1234567890",
  "client_id": "client_12345",
  "machine_id": "machine_12345_1234567890",
  "service": "payment",
  "country": "us",
  "platform": "fixdestar"
}
```

**响应**：
```json
{
  "success": true,
  "data": {
    "phone": "1234567890",
    "remaining_time_display": "10分钟"
  }
}
```

### **验证码获取接口**

**请求**：
```
POST /api/sms
Content-Type: application/json

{
  "api_key": "your-api-key",
  "window_id": "window_12345_1234567890",
  "client_id": "client_12345",
  "phone": "1234567890"
}
```

**响应**：
```json
{
  "success": true,
  "data": {
    "sms_code": "123456",
    "full_text": "Your verification code is 123456",
    "request_time": 1234567890000
  }
}
```

### **手机号码释放接口**

**请求**：
```
POST /api/phone/release
Content-Type: application/json

{
  "api_key": "your-api-key",
  "window_id": "window_12345_1234567890",
  "client_id": "client_12345",
  "phone": "1234567890"
}
```

**响应**：
```json
{
  "success": true
}
```

## ✅ **修正效果**

### **修正前的问题**：
- ❌ 使用文本中的手机号码（可能是错误的）
- ❌ 生成假的验证码
- ❌ 没有真实的API调用

### **修正后的正确流程**：
- ✅ 通过API获取真实手机号码
- ✅ 忽略文本中的手机号码
- ✅ 通过API获取真实验证码
- ✅ 完整的资源管理和错误处理

## 🔍 **验证配置**

运行程序后，查看日志输出：

```
📱 手机号管理器已初始化（API模式）
🆔 窗口ID: window_12345_1234567890
🌐 API地址: https://your-phone-api-domain.com
✅ 手机号码API配置已加载
```

如果看到：
```
⚠️ 手机号码API未配置，请在配置文件中设置api_url和api_key
💡 运行程序选择'3. 配置设置'来配置API
```

说明需要配置API地址和密钥。

## 🎯 **重要提醒**

1. **必须配置真实的API**：不配置API将无法获取手机号码和验证码
2. **API地址格式**：确保API地址以 `http://` 或 `https://` 开头
3. **API密钥安全**：不要在代码中硬编码API密钥，使用配置文件
4. **测试连接**：配置完成后先测试API连接是否正常

## 💡 **基于1.84版本**

当前的手机号码管理器基于你的1.84版本的 `PaymentPhoneManager`，确保：
- 使用相同的API接口规范
- 保持相同的请求格式
- 兼容现有的API服务

**现在系统将真正通过API获取手机号码和验证码，不再使用文本中的假数据！**
